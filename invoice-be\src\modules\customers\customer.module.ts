import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { Customer } from '../../database/entities/customer.entity';
import { User } from '../../database/entities/user.entity';
import { CustomerService } from './services/customer.service';
import { SinvoiceService } from './services/sinvoice.service';
import { CustomerController } from './controllers/customer.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Customer, User]),
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 3,
    }),
  ],
  controllers: [CustomerController],
  providers: [CustomerService, SinvoiceService],
  exports: [CustomerService, SinvoiceService],
})
export class CustomerModule {}
