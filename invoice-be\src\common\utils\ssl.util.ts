import * as fs from 'fs';
import * as path from 'path';
import { Logger } from '@nestjs/common';

const logger = new Logger('SSLUtil');

/**
 * SSL Certificate Management Utilities
 */
export class SSLUtil {
  /**
   * Validates if SSL certificate files exist and are readable
   */
  static validateCertificateFiles(certPaths: {
    ca?: string;
    cert?: string;
    key?: string;
  }): boolean {
    try {
      for (const [type, certPath] of Object.entries(certPaths)) {
        if (certPath) {
          const resolvedPath = path.resolve(certPath);
          if (!fs.existsSync(resolvedPath)) {
            logger.error(`SSL ${type} certificate file not found: ${resolvedPath}`);
            return false;
          }
          
          // Check if file is readable
          fs.accessSync(resolvedPath, fs.constants.R_OK);
          logger.log(`SSL ${type} certificate validated: ${resolvedPath}`);
        }
      }
      return true;
    } catch (error) {
      logger.error('SSL certificate validation failed:', error.message);
      return false;
    }
  }

  /**
   * Creates SSL configuration for cloud providers
   */
  static createCloudSSLConfig(provider: 'aws' | 'gcp' | 'azure' | 'custom'): any {
    const baseConfig = {
      rejectUnauthorized: false, // Most cloud providers use self-signed certificates
    };

    switch (provider) {
      case 'aws':
        return {
          ...baseConfig,
          // AWS RDS specific SSL configuration
          sslmode: 'require',
        };
      
      case 'gcp':
        return {
          ...baseConfig,
          // Google Cloud SQL specific SSL configuration
          sslmode: 'require',
        };
      
      case 'azure':
        return {
          ...baseConfig,
          // Azure Database specific SSL configuration
          sslmode: 'require',
        };
      
      case 'custom':
        return {
          rejectUnauthorized: true, // Custom certificates should be validated
          sslmode: 'verify-full',
        };
      
      default:
        return baseConfig;
    }
  }

  /**
   * Logs SSL connection information for debugging
   */
  static logSSLInfo(sslConfig: any): void {
    if (!sslConfig) {
      logger.log('Database SSL: Disabled');
      return;
    }

    logger.log('Database SSL: Enabled');
    logger.log(`SSL Reject Unauthorized: ${sslConfig.rejectUnauthorized}`);
    
    if (sslConfig.sslmode) {
      logger.log(`SSL Mode: ${sslConfig.sslmode}`);
    }
    
    if (sslConfig.ca) {
      logger.log('SSL CA Certificate: Loaded');
    }
    
    if (sslConfig.cert) {
      logger.log('SSL Client Certificate: Loaded');
    }
    
    if (sslConfig.key) {
      logger.log('SSL Client Key: Loaded');
    }
  }

  /**
   * Creates SSL configuration based on environment
   */
  static createEnvironmentSSLConfig(): any {
    const nodeEnv = process.env.NODE_ENV;
    const sslEnabled = process.env.DB_SSL_ENABLED === 'true';

    if (!sslEnabled) {
      return false;
    }

    // Production environment - enforce SSL
    if (nodeEnv === 'production') {
      const cloudProvider = process.env.DB_CLOUD_PROVIDER as 'aws' | 'gcp' | 'azure' | 'custom';
      
      if (cloudProvider) {
        return this.createCloudSSLConfig(cloudProvider);
      }
      
      // Default production SSL config
      return {
        rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
        sslmode: process.env.DB_SSL_MODE || 'require',
      };
    }

    // Development/Test environment - flexible SSL
    return {
      rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED === 'true',
      sslmode: process.env.DB_SSL_MODE || 'prefer',
    };
  }
}
