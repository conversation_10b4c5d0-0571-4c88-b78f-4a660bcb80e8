{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/auth/login-form.tsx", "../../src/components/auth/protected-route.tsx", "../../src/components/auth/__tests__/login-form.test.tsx", "../../src/components/customers/create-customer-form.tsx", "../../src/components/customers/customer-details.tsx", "../../src/components/customers/customer-list.tsx", "../../src/components/customers/edit-customer-form.tsx", "../../src/components/customers/index.ts", "../../src/components/customers/shared/form-fields.tsx", "../../src/components/invoices/column-selector.tsx", "../../src/components/invoices/invoice-filters.tsx", "../../src/components/invoices/invoice-table.tsx", "../../src/components/layout/app-sidebar.tsx", "../../src/components/layout/dashboard-layout.tsx", "../../src/components/layout/navbar.tsx", "../../src/components/ui/alert-dialog.tsx", "../../src/components/ui/alert.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/form.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/pagination.test.tsx", "../../src/components/ui/pagination.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/sheet.tsx", "../../src/components/ui/sidebar.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/sonner.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/tooltip.tsx", "../../src/hooks/use-mobile.ts", "../../src/hooks/use-network-status.ts", "../../src/hooks/use-session-management.ts", "../../src/lib/api.ts", "../../src/lib/utils.ts", "../../src/lib/validation.ts", "../../src/pages/customers.tsx", "../../src/pages/dashboard.tsx", "../../src/pages/invoices.tsx", "../../src/pages/login.tsx", "../../src/pages/profile.tsx", "../../src/pages/customers/create.tsx", "../../src/pages/customers/details.tsx", "../../src/pages/customers/edit.tsx", "../../src/services/auth.service.ts", "../../src/services/customer.service.ts", "../../src/services/invoice.service.ts", "../../src/stores/auth.store.ts", "../../src/stores/customer.store.ts", "../../src/stores/invoice.store.ts", "../../src/stores/__tests__/auth.store.test.ts", "../../src/test/setup.ts", "../../src/types/api.ts", "../../src/types/auth.ts", "../../src/types/customer.ts", "../../src/types/index.ts", "../../src/types/invoice.ts"], "errors": true, "version": "5.8.3"}