import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "../ui/card";
import { Separator } from "../ui/separator";
import { Form } from "../ui/form";
import { Button } from "../ui/button";
import {
  EditTextField,
  EditPasswordField,
  EditCheckboxField,
} from "./shared/form-fields";
import {
  UpdateCustomerFormData,
  updateCustomerSchema,
} from "../../lib/validation";
import { Customer, UpdateCustomerRequest } from "../../types";

interface EditCustomerFormProps {
  customer: Customer;
  isLoading?: boolean;
  onSubmit: (data: UpdateCustomerRequest) => Promise<void>;
  onCancel: () => void;
}

export function EditCustomerForm({
  customer,
  isLoading = false,
  onSubmit,
  onCancel,
}: EditCustomerFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showSinvoicePassword, setShowSinvoicePassword] = useState(false);

  const form = useForm<UpdateCustomerFormData>({
    resolver: zodResolver(updateCustomerSchema),
    defaultValues: {
      username: customer.user.username as string | undefined,
      email: customer.user.email as string | undefined,
      password: undefined,
      isActive: customer.user.isActive,
      sinvoiceUsername: customer.sinvoiceUsername as string | undefined,
      sinvoicePassword: undefined,
    },
  });

  const handleFormSubmit = async (data: UpdateCustomerFormData) => {
    try {
      // Remove empty optional fields
      const cleanedData = Object.entries(data).reduce((acc, [key, value]) => {
        if (value !== "" && value !== undefined && value !== null) {
          const k = key as keyof UpdateCustomerRequest;
          if (k === "isActive") {
            (acc as any)[k] = value;
          } else if (typeof value === "string" && value.trim() !== "") {
            (acc as any)[k] = value;
          }
        }
        return acc;
      }, {} as UpdateCustomerRequest);

      await onSubmit(cleanedData);
      toast.success("Customer updated successfully");
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message;

      if (error.response?.status === 409) {
        toast.error("Duplicate data detected", {
          description: errorMessage || "Username or email already exists.",
        });
      } else if (error.response?.status === 400) {
        toast.error("Validation failed", {
          description: errorMessage || "Please check your input and try again.",
        });
      } else {
        toast.error("Failed to update customer", {
          description: errorMessage || "Please try again later.",
        });
      }
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Edit Customer</CardTitle>
        <CardDescription>
          Update customer information and Sinvoice integration
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          <CardContent className="space-y-6">
            {/* System Account Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">System Account</h3>
              <EditTextField
                form={form}
                name="username"
                label="Username"
                isLoading={isLoading}
              />
              <EditPasswordField
                form={form}
                name="password"
                label="New Password"
                showPassword={showPassword}
                onTogglePassword={() => setShowPassword(!showPassword)}
                isLoading={isLoading}
              />
              <EditTextField
                form={form}
                name="email"
                label="Email"
                type="email"
                isLoading={isLoading}
              />
              <EditCheckboxField
                form={form}
                name="isActive"
                label="Account is active"
                isLoading={isLoading}
              />
            </div>

            <Separator />

            {/* Sinvoice Integration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Sinvoice Integration</h3>
              <EditTextField
                form={form}
                name="sinvoiceUsername"
                label="Sinvoice Username"
                isLoading={isLoading}
              />
              <EditPasswordField
                form={form}
                name="sinvoicePassword"
                label="New Sinvoice Password"
                showPassword={showSinvoicePassword}
                onTogglePassword={() =>
                  setShowSinvoicePassword(!showSinvoicePassword)
                }
                isLoading={isLoading}
              />
            </div>
          </CardContent>

          <CardFooter className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              Update Customer
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
