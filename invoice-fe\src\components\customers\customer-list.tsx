import { useEffect, useState } from "react";
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Key,
  Eye,
  Filter,
  RefreshCw,
  Trash2,
} from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Badge } from "../ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { useCustomerStore } from "../../stores/customer.store";
import { Customer } from "../../types";
import { formatDistanceToNow } from "date-fns";

interface CustomerListProps {
  onCreateCustomer: () => void;
  onEditCustomer: (customer: Customer) => void;
  onViewCustomer: (customer: Customer) => void;
  onResetToken: (customer: Customer) => void;
  onDeleteCustomer: (customer: Customer) => void;
}

export function CustomerList({
  onCreateCustomer,
  onEditCustomer,
  onViewCustomer,
  onResetToken,
  onDeleteCustomer,
}: CustomerListProps) {
  const {
    customers,
    isLoading,
    error,
    filters,
    pagination,
    fetchCustomers,
    setFilters,
    setPage,
    setLimit,
    clearError,
  } = useCustomerStore();

  const [searchTerm, setSearchTerm] = useState(filters.search || "");

  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  useEffect(() => {
    if (error) {
      // Error is already shown via toast in store
      clearError();
    }
  }, [error, clearError]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setFilters({ search: value || undefined });
  };

  const handleFilterChange = (key: keyof typeof filters, value: any) => {
    setFilters({ [key]: value === "all" ? undefined : value });
  };

  const handleRefresh = () => {
    fetchCustomers();
  };

  const getStatusBadge = (customer: Customer) => {
    if (!customer.user.isActive) {
      return <Badge variant="destructive">Inactive</Badge>;
    }
    if (customer.hasSinvoiceTokens) {
      return <Badge variant="default">Connected</Badge>;
    }
    return <Badge variant="secondary">Active</Badge>;
  };

  const formatDate = (date: Date | string) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Customer Management
          </h1>
          <p className="text-muted-foreground">
            Manage customer accounts and API access
          </p>
        </div>
        <Button onClick={onCreateCustomer} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Customer
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
          <CardDescription>Search and filter customers</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search customers..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <Select
              value={
                filters.isActive === undefined
                  ? "all"
                  : filters.isActive.toString()
              }
              onValueChange={(value) =>
                handleFilterChange(
                  "isActive",
                  value === "all" ? undefined : value === "true"
                )
              }
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="true">Active</SelectItem>
                <SelectItem value="false">Inactive</SelectItem>
              </SelectContent>
            </Select>

            {/* Connection Filter */}
            <Select
              value={
                filters.hasSinvoiceTokens === undefined
                  ? "all"
                  : filters.hasSinvoiceTokens.toString()
              }
              onValueChange={(value) =>
                handleFilterChange(
                  "hasSinvoiceTokens",
                  value === "all" ? undefined : value === "true"
                )
              }
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Connection" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="true">Connected</SelectItem>
                <SelectItem value="false">Not Connected</SelectItem>
              </SelectContent>
            </Select>

            {/* Refresh Button */}
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw
                className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Customer Table */}
      <Card>
        <CardHeader>
          <CardTitle>Customers ({customers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center space-x-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                <span className="text-sm text-muted-foreground">
                  Loading customers...
                </span>
              </div>
            </div>
          ) : customers.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No customers found</p>
              <Button onClick={onCreateCustomer} className="mt-4">
                Create your first customer
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer</TableHead>
                  <TableHead>Sinvoice Username</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>API Token</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {customers.map((customer) => (
                  <TableRow
                    key={customer.id}
                    onClick={() => onViewCustomer(customer)}
                    className="cursor-pointer"
                  >
                    <TableCell>
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {customer.sinvoiceUsername}
                      </code>
                    </TableCell>
                    <TableCell>{getStatusBadge(customer)}</TableCell>
                    <TableCell>
                      <code className="text-xs bg-muted px-2 py-1 rounded">
                        {customer.apiToken.substring(0, 8)}...
                      </code>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDate(customer.createdAt)}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => onViewCustomer(customer)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onEditCustomer(customer)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Customer
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onResetToken(customer)}
                            className="text-orange-600"
                          >
                            <Key className="mr-2 h-4 w-4" />
                            Reset API Token
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onDeleteCustomer(customer)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Customer
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
