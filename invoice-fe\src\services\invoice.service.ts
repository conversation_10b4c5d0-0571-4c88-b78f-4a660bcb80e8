import api from "../lib/api";
import { GetInvoicesRequest, GetInvoicesResponse, Invoice } from "../types";

export class InvoiceService {
  private static readonly BASE_PATH = "/invoices";

  /**
   * Get invoices using the backend API
   * This method calls the backend which then calls the Sinvoice API
   */
  static async getInvoices(request: GetInvoicesRequest): Promise<GetInvoicesResponse> {
    try {
      // The backend expects the API token in the request body, not as a header
      const response = await api.post<GetInvoicesResponse>(
        `${this.BASE_PATH}/list`,
        request,
        {
          timeout: 35000, // 35 seconds to account for backend timeout of 30s
        }
      );

      return response.data;
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 401) {
        throw new Error("Invalid API token. Please check your credentials.");
      }
      
      if (error.response?.status === 400) {
        const errorDetails = error.response?.data?.details || [];
        if (Array.isArray(errorDetails) && errorDetails.length > 0) {
          throw new Error(`Validation error: ${errorDetails.join(", ")}`);
        }
        throw new Error(error.response?.data?.message || "Invalid request format");
      }

      if (error.response?.status === 429) {
        throw new Error("Rate limit exceeded. Please wait before making another request.");
      }

      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new Error("Request timeout. The invoice service may be temporarily unavailable.");
      }

      // Generic error handling
      const errorMessage = error.response?.data?.message || error.message || "Failed to fetch invoices";
      throw new Error(errorMessage);
    }
  }

  /**
   * Validate invoice filters before sending to API
   */
  static validateFilters(request: GetInvoicesRequest): string[] {
    const errors: string[] = [];

    // Required field validation
    if (!request.apiToken?.trim()) {
      errors.push("API token is required");
    }

    if (!request.startDate?.trim()) {
      errors.push("Start date is required");
    }

    if (!request.endDate?.trim()) {
      errors.push("End date is required");
    }

    if (!request.rowPerPage || request.rowPerPage < 1) {
      errors.push("Rows per page must be at least 1");
    }

    if (!request.pageNum || request.pageNum < 1) {
      errors.push("Page number must be at least 1");
    }

    // Optional field validation
    if (request.invoiceNo && request.invoiceNo.trim()) {
      if (request.invoiceNo.length < 7 || request.invoiceNo.length > 35) {
        errors.push("Invoice number must be between 7 and 35 characters");
      }
      if (!/^[a-zA-Z0-9]*$/.test(request.invoiceNo)) {
        errors.push("Invoice number must contain only alphanumeric characters");
      }
    }

    if (request.invoiceSeri && request.invoiceSeri.trim()) {
      if (request.invoiceSeri.length > 25) {
        errors.push("Invoice series must not exceed 25 characters");
      }
      if (!/^[a-zA-Z0-9]*$/.test(request.invoiceSeri)) {
        errors.push("Invoice series must contain only alphanumeric characters");
      }
    }

    if (request.buyerTaxCode && request.buyerTaxCode.length > 20) {
      errors.push("Buyer tax code must not exceed 20 characters");
    }

    // Date validation
    const dateFields = [
      { field: request.startDate, name: "Start date" },
      { field: request.endDate, name: "End date" },
      { field: request.issueStartDate, name: "Issue start date" },
      { field: request.issueEndDate, name: "Issue end date" },
    ];

    dateFields.forEach(({ field, name }) => {
      if (field && field.trim()) {
        if (field.length > 50) {
          errors.push(`${name} must not exceed 50 characters`);
        }
        // Basic date format validation (YYYY-MM-DD)
        if (!/^\d{4}-\d{2}-\d{2}$/.test(field)) {
          errors.push(`${name} must be in YYYY-MM-DD format`);
        }
      }
    });

    // Date range validation
    if (request.startDate && request.endDate) {
      const startDate = new Date(request.startDate);
      const endDate = new Date(request.endDate);
      
      if (startDate > endDate) {
        errors.push("Start date must be before or equal to end date");
      }
    }

    if (request.issueStartDate && request.issueEndDate) {
      const issueStartDate = new Date(request.issueStartDate);
      const issueEndDate = new Date(request.issueEndDate);
      
      if (issueStartDate > issueEndDate) {
        errors.push("Issue start date must be before or equal to issue end date");
      }
    }

    return errors;
  }

  /**
   * Get invoices with validation
   */
  static async getInvoicesWithValidation(request: GetInvoicesRequest): Promise<GetInvoicesResponse> {
    // Validate the request first
    const validationErrors = this.validateFilters(request);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(", ")}`);
    }

    return this.getInvoices(request);
  }

  /**
   * Format invoice data for display
   */
  static formatInvoice(invoice: Invoice): Invoice {
    return {
      ...invoice,
      // Ensure dates are properly formatted
      issueDate: invoice.issueDate ? new Date(invoice.issueDate).toISOString() : '',
      createdDate: invoice.createdDate ? new Date(invoice.createdDate).toISOString() : undefined,
      modifiedDate: invoice.modifiedDate ? new Date(invoice.modifiedDate).toISOString() : undefined,
      // Ensure numeric fields are numbers
      total: typeof invoice.total === 'string' ? parseFloat(invoice.total) : invoice.total,
    };
  }

  /**
   * Format multiple invoices
   */
  static formatInvoices(invoices: Invoice[]): Invoice[] {
    return invoices.map(invoice => this.formatInvoice(invoice));
  }

  /**
   * Get default filter values
   */
  static getDefaultFilters(): Partial<GetInvoicesRequest> {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    return {
      startDate: thirtyDaysAgo.toISOString().split('T')[0], // YYYY-MM-DD format
      endDate: today.toISOString().split('T')[0], // YYYY-MM-DD format
      rowPerPage: 10,
      pageNum: 1,
    };
  }

  /**
   * Build query parameters for URL
   */
  static buildQueryParams(request: GetInvoicesRequest): URLSearchParams {
    const params = new URLSearchParams();
    
    // Add all non-empty parameters
    Object.entries(request).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    return params;
  }

  /**
   * Parse query parameters from URL
   */
  static parseQueryParams(searchParams: URLSearchParams): Partial<GetInvoicesRequest> {
    const filters: Partial<GetInvoicesRequest> = {};

    // Parse string parameters
    const stringParams = ['apiToken', 'startDate', 'endDate', 'invoiceNo', 'invoiceType', 'buyerTaxCode', 'buyerIdNo', 'templateCode', 'invoiceSeri', 'issueStartDate', 'issueEndDate'];
    stringParams.forEach(param => {
      const value = searchParams.get(param);
      if (value) {
        (filters as any)[param] = value;
      }
    });

    // Parse numeric parameters
    const numericParams = ['rowPerPage', 'pageNum'];
    numericParams.forEach(param => {
      const value = searchParams.get(param);
      if (value && !isNaN(Number(value))) {
        (filters as any)[param] = Number(value);
      }
    });

    // Parse boolean parameters
    const booleanParams = ['getAll'];
    booleanParams.forEach(param => {
      const value = searchParams.get(param);
      if (value === 'true' || value === 'false') {
        (filters as any)[param] = value === 'true';
      }
    });

    return filters;
  }
}
