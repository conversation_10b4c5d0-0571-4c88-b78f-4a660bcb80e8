import {
  Controller,
  Post,
  Body,
  Headers,
  UnauthorizedException,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiHeader,
  ApiBody,
  ApiSecurity,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { InvoiceService } from '../services/invoice.service';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { Public } from '../../../common/decorators/public.decorator';
import { GetInvoicesDto } from '../dto/get-invoices.dto';

@ApiTags('Invoices')
@Controller('invoices')
export class InvoiceController {
  constructor(private readonly invoiceService: InvoiceService) {}

  @Post('create')
  @Public()
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 attempts per minute
  @ApiOperation({
    summary: 'Create a new invoice using Sinvoice API',
    description: `Creates a new invoice using customer credentials and Sinvoice API.
    
This endpoint requires customer authentication using an API token. The token should be included in the X-API-Token header.
The API token can be obtained from the customer's profile in the system.

The endpoint will:
1. Validate the API token
2. Use the customer's stored Sinvoice credentials for authentication
3. Create the invoice through Sinvoice API
4. Return the Sinvoice API response

Rate limit: 5 requests per minute per customer.`,
  })
  @ApiSecurity('X-API-Token')
  @ApiBody({ type: CreateInvoiceDto })
  @ApiResponse({
    status: 200,
    description: 'Invoice created successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Invoice created successfully',
        data: {
          invoiceId: '*********',
          invoiceNumber: 'AA/20E-0001',
          createdAt: '2024-03-20T10:00:00.000Z',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid request format',
    schema: {
      example: {
        statusCode: 400,
        message: 'Invalid request format',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid API token or Sinvoice credentials',
    schema: {
      example: {
        statusCode: 401,
        message: 'Invalid API token',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error - Sinvoice API connection failed',
    schema: {
      example: {
        statusCode: 500,
        message: 'Sinvoice API connection failed',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests - rate limit exceeded',
    schema: {
      example: {
        statusCode: 429,
        message: 'Too Many Requests',
        error: 'Rate limit exceeded. Try again in 60 seconds.',
      },
    },
  })
  async createInvoice(
    @Headers('x-api-token') apiToken: string,
    @Body() createInvoiceDto: CreateInvoiceDto,
  ): Promise<any> {
    if (!apiToken) {
      throw new UnauthorizedException('API token is required');
    }

    return this.invoiceService.createInvoice(apiToken, createInvoiceDto);
  }

  @Post('list')
  @Public()
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 attempts per minute
  @ApiOperation({
    summary: 'Get invoices using Sinvoice API',
    description: `Retrieves invoices using customer credentials and Sinvoice API.
    
This endpoint requires customer authentication using an API token provided in the request body.
The API token can be obtained from the customer's profile in the system.

The endpoint will:
1. Validate the API token
2. Use the customer's stored Sinvoice credentials for authentication
3. Retrieve invoices through Sinvoice API
4. Return the Sinvoice API response

Rate limit: 10 requests per minute per customer.`,
  })
  @ApiResponse({
    status: 200,
    description: 'Invoices retrieved successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Invoices retrieved successfully',
        data: {
          totalRecords: 100,
          pageNum: 0,
          rowPerPage: 10,
          invoices: [
            {
              invoiceId: '*********',
              invoiceNo: 'AA/20E-0001',
              issueDate: '2024-03-20T10:00:00.000Z',
              buyerTaxCode: '0*********',
              total: 1000000,
              status: 'SIGNED',
            },
          ],
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid request format',
    schema: {
      example: {
        statusCode: 400,
        message: 'Invalid request format',
        error: 'Bad Request',
        details: [
          'API token is required',
          'Start date is required',
          'End date is required',
          'Rows per page must be at least 1',
        ],
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid API token',
    schema: {
      example: {
        statusCode: 401,
        message: 'Invalid API token',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error - Sinvoice API connection failed',
    schema: {
      example: {
        statusCode: 500,
        message: 'Sinvoice API connection failed',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests - rate limit exceeded',
    schema: {
      example: {
        statusCode: 429,
        message: 'Too Many Requests',
        error: 'Rate limit exceeded. Try again in 60 seconds.',
      },
    },
  })
  async getInvoices(@Body() getInvoicesDto: GetInvoicesDto): Promise<any> {
    const { apiToken, ...searchParams } = getInvoicesDto;
    if (!apiToken) {
      throw new UnauthorizedException('API token is required');
    }

    return this.invoiceService.getInvoices(apiToken, searchParams);
  }
}
