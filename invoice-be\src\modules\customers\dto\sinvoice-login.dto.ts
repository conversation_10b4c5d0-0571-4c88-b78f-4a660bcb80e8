import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SinvoiceLoginDto {
  @ApiProperty({
    description: 'Customer API token for authentication',
    example: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2',
  })
  @IsString()
  @IsNotEmpty()
  apiToken: string;
}

export class SinvoiceLoginResponseDto {
  @ApiProperty({
    description: 'Sinvoice access token',
    example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Token type',
    example: 'bearer',
  })
  tokenType: string;

  @ApiProperty({
    description: 'Sinvoice refresh token',
    example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 1199,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'Token scope',
    example: 'openid',
  })
  scope: string;

  @ApiProperty({
    description: 'Token issued at timestamp',
    example: **********,
  })
  iat: number;

  @ApiProperty({
    description: 'Invoice cluster identifier',
    example: 'cluster7',
  })
  invoiceCluster: string;

  @ApiProperty({
    description: 'Account type',
    example: 1,
  })
  type: number;

  @ApiProperty({
    description: 'JWT ID',
    example: '3d4a5f1f-80ab-4596-af34-24c78bc60e5b',
  })
  jti: string;

  @ApiProperty({
    description: 'Success message',
    example: 'Sinvoice login successful',
  })
  message: string;
}

export interface SinvoiceApiLoginRequest {
  username: string;
  password: string;
}

export interface SinvoiceApiLoginResponse {
  access_token: string;
  token_type: string;
  refresh_token: string;
  expires_in: number;
  scope: string;
  iat: number;
  invoice_cluster: string;
  type: number;
  jti: string;
}
