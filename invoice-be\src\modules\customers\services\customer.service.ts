import {
  Injectable,
  ConflictException,
  NotFoundException,
  Logger,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Customer } from '../../../database/entities/customer.entity';
import { User, UserRole } from '../../../database/entities/user.entity';
import {
  CreateCustomerDto,
  CustomerResponseDto,
  UpdateCustomerDto,
  ApiTokenResponseDto,
  SinvoiceLoginResponseDto,
} from '../dto';
import { SinvoiceService } from './sinvoice.service';

@Injectable()
export class CustomerService {
  private readonly logger = new Logger(CustomerService.name);

  constructor(
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
    private sinvoiceService: SinvoiceService,
  ) {}

  /**
   * Create a new customer account
   */
  async createCustomer(
    createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const { username, email, password, sinvoiceUsername, sinvoicePassword } =
      createCustomerDto;

    // Check if username or email already exists
    const existingUser = await this.userRepository.findOne({
      where: [{ username }, { email }],
    });

    if (existingUser) {
      if (existingUser.username === username) {
        throw new ConflictException('Username already exists');
      }
      if (existingUser.email === email) {
        throw new ConflictException('Email already exists');
      }
    }

    // Use transaction to ensure data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create user account
      const user = this.userRepository.create({
        username,
        email,
        password,
        role: UserRole.CUSTOMER,
        isActive: true,
      });

      const savedUser = await queryRunner.manager.save(user);

      // Create customer record
      const customer = this.customerRepository.create({
        userId: savedUser.id,
        sinvoiceUsername,
      });

      // Set encrypted Sinvoice password
      customer.setSinvoicePassword(sinvoicePassword);

      const savedCustomer = await queryRunner.manager.save(customer);

      await queryRunner.commitTransaction();

      this.logger.log(
        `Customer created successfully: ${username} (${savedUser.id})`,
      );

      // Return customer with user data
      const customerWithUser = await this.customerRepository.findOne({
        where: { id: savedCustomer.id },
        relations: ['user'],
      });

      return this.mapToCustomerResponse(customerWithUser!);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to create customer: ${username}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomerById(id: string): Promise<CustomerResponseDto> {
    const customer = await this.customerRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    return this.mapToCustomerResponse(customer);
  }

  /**
   * Get customer by user ID
   */
  async getCustomerByUserId(userId: string): Promise<CustomerResponseDto> {
    const customer = await this.customerRepository.findOne({
      where: { userId },
      relations: ['user'],
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    return this.mapToCustomerResponse(customer);
  }

  /**
   * Get customer by API token
   */
  async getCustomerByApiToken(apiToken: string): Promise<Customer | null> {
    return this.customerRepository.findOne({
      where: { apiToken },
      relations: ['user'],
    });
  }

  /**
   * Update customer information
   */
  async updateCustomer(
    id: string,
    updateCustomerDto: UpdateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const customer = await this.customerRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    const {
      username,
      email,
      password,
      isActive,
      sinvoiceUsername,
      sinvoicePassword,
    } = updateCustomerDto;

    // Use transaction for consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update user information if provided
      if (username && username !== customer.user.username) {
        // Check if username already exists
        const existingUser = await this.userRepository.findOne({
          where: { username },
        });

        if (existingUser && existingUser.id !== customer.userId) {
          throw new ConflictException('Username already exists');
        }

        customer.user.username = username;
      }

      if (email && email !== customer.user.email) {
        // Check if email already exists
        const existingUser = await this.userRepository.findOne({
          where: { email },
        });

        if (existingUser && existingUser.id !== customer.userId) {
          throw new ConflictException('Email already exists');
        }

        customer.user.email = email;
      }

      if (password) {
        customer.user.password = password; // Password will be hashed by entity subscriber
      }

      if (typeof isActive === 'boolean') {
        customer.user.isActive = isActive;
      }

      // Save user changes if any
      if (username || email || password || typeof isActive === 'boolean') {
        await queryRunner.manager.save(customer.user);
      }

      // Update Sinvoice credentials if provided
      if (sinvoiceUsername) {
        customer.sinvoiceUsername = sinvoiceUsername;
      }

      if (sinvoicePassword) {
        customer.setSinvoicePassword(sinvoicePassword);
        // Clear existing Sinvoice tokens when password changes
        customer.clearSinvoiceTokens();
      }

      const savedCustomer = await queryRunner.manager.save(customer);
      await queryRunner.commitTransaction();

      this.logger.log(
        `Customer updated successfully: ${customer.user.username} (${id})`,
      );

      // Return updated customer
      const updatedCustomer = await this.customerRepository.findOne({
        where: { id },
        relations: ['user'],
      });

      return this.mapToCustomerResponse(updatedCustomer!);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update customer: ${id}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Reset customer API token (admin only)
   */
  async resetApiToken(id: string): Promise<ApiTokenResponseDto> {
    const customer = await this.customerRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    // Generate new API token
    customer.regenerateApiToken();
    await this.customerRepository.save(customer);

    this.logger.log(
      `API token reset for customer: ${customer.user.username} (${id})`,
    );

    return {
      apiToken: customer.apiToken,
      message: 'API token reset successfully',
    };
  }

  /**
   * Update Sinvoice session tokens
   */
  async updateSinvoiceTokens(
    id: string,
    accessToken: string,
    refreshToken: string,
    expiresIn: number,
  ): Promise<void> {
    const customer = await this.customerRepository.findOne({
      where: { id },
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    customer.updateSinvoiceTokens(accessToken, refreshToken, expiresIn);
    await this.customerRepository.save(customer);

    this.logger.log(`Sinvoice tokens updated for customer: ${id}`);
  }

  /**
   * Get all customers (admin only)
   */
  async getAllCustomers(): Promise<CustomerResponseDto[]> {
    const customers = await this.customerRepository.find({
      relations: ['user'],
      order: { createdAt: 'DESC' },
    });

    return customers.map((customer) => this.mapToCustomerResponse(customer));
  }

  /**
   * Authenticate customer with Sinvoice using API token
   */
  async sinvoiceLogin(apiToken: string): Promise<SinvoiceLoginResponseDto> {
    // Find customer by API token
    const customer = await this.getCustomerByApiToken(apiToken);
    if (!customer) {
      throw new UnauthorizedException('Invalid API token');
    }

    try {
      // Migrate legacy password if needed
      if (!customer.sinvoicePasswordAuthTag) {
        this.logger.log(
          `Migrating legacy password for customer: ${customer.user.username}`,
        );
        customer.migrateLegacyPassword();
        await this.customerRepository.save(customer);
      }

      // Get decrypted Sinvoice credentials
      const sinvoiceUsername = customer.sinvoiceUsername;
      const sinvoicePassword = customer.getSinvoicePassword();

      // Authenticate with Sinvoice API
      const sinvoiceResponse = await this.sinvoiceService.login(
        sinvoiceUsername,
        sinvoicePassword,
      );

      // Update customer's Sinvoice tokens
      await this.updateSinvoiceTokens(
        customer.id,
        sinvoiceResponse.access_token,
        sinvoiceResponse.refresh_token,
        sinvoiceResponse.expires_in,
      );

      this.logger.log(
        `Sinvoice login successful for customer: ${customer.user.username}`,
      );

      // Return formatted response
      return {
        accessToken: sinvoiceResponse.access_token,
        tokenType: sinvoiceResponse.token_type,
        refreshToken: sinvoiceResponse.refresh_token,
        expiresIn: sinvoiceResponse.expires_in,
        scope: sinvoiceResponse.scope,
        iat: sinvoiceResponse.iat,
        invoiceCluster: sinvoiceResponse.invoice_cluster,
        type: sinvoiceResponse.type,
        jti: sinvoiceResponse.jti,
        message: 'Sinvoice login successful',
      };
    } catch (error) {
      this.logger.error(
        `Sinvoice login failed for customer: ${customer.user.username}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Refresh Sinvoice token for customer
   */
  async refreshSinvoiceToken(
    customerId: string,
  ): Promise<SinvoiceLoginResponseDto> {
    const customer = await this.customerRepository.findOne({
      where: { id: customerId },
      relations: ['user'],
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    if (!customer.sinvoiceRefreshToken) {
      throw new BadRequestException('No refresh token available');
    }

    try {
      const sinvoiceResponse = await this.sinvoiceService.refreshToken(
        customer.sinvoiceRefreshToken,
      );

      // Update customer's Sinvoice tokens
      await this.updateSinvoiceTokens(
        customer.id,
        sinvoiceResponse.access_token,
        sinvoiceResponse.refresh_token,
        sinvoiceResponse.expires_in,
      );

      this.logger.log(
        `Sinvoice token refreshed for customer: ${customer.user.username}`,
      );

      return {
        accessToken: sinvoiceResponse.access_token,
        tokenType: sinvoiceResponse.token_type,
        refreshToken: sinvoiceResponse.refresh_token,
        expiresIn: sinvoiceResponse.expires_in,
        scope: sinvoiceResponse.scope,
        iat: sinvoiceResponse.iat,
        invoiceCluster: sinvoiceResponse.invoice_cluster,
        type: sinvoiceResponse.type,
        jti: sinvoiceResponse.jti,
        message: 'Sinvoice token refreshed successfully',
      };
    } catch (error) {
      this.logger.error(
        `Sinvoice token refresh failed for customer: ${customer.user.username}`,
        error.stack,
      );

      // Clear invalid tokens
      customer.clearSinvoiceTokens();
      await this.customerRepository.save(customer);

      throw error;
    }
  }

  /**
   * Delete customer and associated user account
   */
  async deleteCustomer(id: string): Promise<void> {
    const customer = await this.customerRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    // Use transaction to ensure data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Delete customer record first (due to foreign key constraint)
      await queryRunner.manager.remove(customer);

      // Delete associated user account
      await queryRunner.manager.remove(customer.user);

      await queryRunner.commitTransaction();

      this.logger.log(
        `Customer deleted successfully: ${customer.user.username} (${id})`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to delete customer: ${id}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Map Customer entity to response DTO
   */
  private mapToCustomerResponse(customer: Customer): CustomerResponseDto {
    return {
      id: customer.id,
      user: {
        id: customer.user.id,
        username: customer.user.username,
        email: customer.user.email,
        role: customer.user.role,
        isActive: customer.user.isActive,
        lastLoginAt: customer.user.lastLoginAt,
        createdAt: customer.user.createdAt,
        updatedAt: customer.user.updatedAt,
      },
      sinvoiceUsername: customer.sinvoiceUsername,
      apiToken: customer.apiToken,
      hasSinvoiceTokens:
        !customer.isSinvoiceTokenExpired() && !!customer.sinvoiceAccessToken,
      sinvoiceTokenExpiresAt: customer.sinvoiceTokenExpiresAt,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    };
  }
}
