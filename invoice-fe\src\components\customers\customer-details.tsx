import { useState } from "react";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { Badge } from "../ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Separator } from "../ui/separator";
import { Customer } from "../../types";
import { formatDistanceToNow, format } from "date-fns";
import { toast } from "sonner";
import { Button } from "../ui/button";

interface CustomerDetailsProps {
  customer: Customer;
  onEdit: () => void;
  onResetToken: () => void;
  isLoading?: boolean;
}

export function CustomerDetails({
  customer,
  onEdit,
  onResetToken,
  isLoading,
}: CustomerDetailsProps) {
  const [showResetDialog, setShowResetDialog] = useState(false);

  const copyToClipboard = async (text: string, label: string) => {
    try {
      if (!navigator.clipboard) {
        // Fallback for browsers that don't support clipboard API
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        toast.success(`${label} copied to clipboard`);
        return;
      }

      await navigator.clipboard.writeText(text);
      toast.success(`${label} copied to clipboard`, {
        description: `${
          text.length > 50 ? text.substring(0, 50) + "..." : text
        }`,
      });
    } catch (error: any) {
      console.error("Failed to copy to clipboard:", error);
      toast.error("Failed to copy to clipboard", {
        description: "Please try selecting and copying manually.",
      });
    }
  };

  const handleResetToken = () => {
    setShowResetDialog(false);
    onResetToken();
  };

  const getStatusInfo = () => {
    if (!customer.user.isActive) {
      return {
        icon: XCircle,
        label: "Inactive",
        variant: "destructive" as const,
        description: "Account is deactivated",
      };
    }
    if (customer.hasSinvoiceTokens) {
      return {
        icon: CheckCircle,
        label: "Connected",
        variant: "default" as const,
        description: "Sinvoice integration active",
      };
    }
    return {
      icon: AlertCircle,
      label: "Active",
      variant: "secondary" as const,
      description: "Account active, Sinvoice not connected",
    };
  };

  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;

  const formatDate = (date: Date | string) => {
    const dateObj = new Date(date);
    return {
      relative: formatDistanceToNow(dateObj, { addSuffix: true }),
      absolute: dateObj.toLocaleString(),
    };
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Customer Details</CardTitle>
        <CardDescription>
          View customer information and integration details
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* System Account Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">System Account</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Username</p>
              <p className="mt-1">{customer.user.username}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p className="mt-1">{customer.user.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <div className="mt-1">
                <Badge
                  variant={customer.user.isActive ? "default" : "destructive"}
                >
                  {customer.user.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Role</p>
              <p className="mt-1 capitalize">
                {customer.user.role.toLowerCase()}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Last Login</p>
              <p className="mt-1">
                {customer.user.lastLoginAt
                  ? format(new Date(customer.user.lastLoginAt), "PPpp")
                  : "Never"}
              </p>
            </div>
          </div>
        </div>

        <Separator />

        {/* Sinvoice Integration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Sinvoice Integration</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">
                Sinvoice Username
              </p>
              <p className="mt-1">{customer.sinvoiceUsername}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">
                API Token Status
              </p>
              <div className="mt-1">
                <Badge
                  variant={
                    customer.hasSinvoiceTokens ? "default" : "destructive"
                  }
                >
                  {customer.hasSinvoiceTokens ? "Valid" : "Invalid"}
                </Badge>
              </div>
            </div>
            <div className="col-span-2">
              <p className="text-sm font-medium text-gray-500">API Token</p>
              <p className="mt-1 font-mono text-sm break-all">
                {customer.apiToken || "Not available"}
              </p>
            </div>
            {customer.sinvoiceTokenExpiresAt && (
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Token Expires At
                </p>
                <p className="mt-1">
                  {format(new Date(customer.sinvoiceTokenExpiresAt), "PPpp")}
                </p>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Metadata */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Metadata</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Created At</p>
              <p className="mt-1">
                {format(new Date(customer.createdAt), "PPpp")}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Last Updated</p>
              <p className="mt-1">
                {format(new Date(customer.updatedAt), "PPpp")}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end space-x-4">
        <Button variant="outline" onClick={onEdit}>
          Edit
        </Button>
        <Button onClick={onResetToken}>Reset Token</Button>
      </CardFooter>
    </Card>
  );
}
