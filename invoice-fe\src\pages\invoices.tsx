import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import { AlertCircle, RefreshCw } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Pagination } from "@/components/ui/pagination";

import { InvoiceFilters } from "../components/invoices/invoice-filters";
import { InvoiceTable } from "../components/invoices/invoice-table";
import { 
  useInvoiceStore,
  useInvoiceData,
  useInvoiceFilters,
  useInvoiceTable,
  useInvoicePagination,
  useCustomerApiToken
} from "../stores/invoice.store";
import { 
  DEFAULT_INVOICE_COLUMNS,
  GetInvoicesRequest,
  InvoiceFilters as IInvoiceFilters
} from "../types";
import { InvoiceService } from "../services/invoice.service";
import { useAuthStore } from "../stores/auth.store";

export function InvoicesPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useAuthStore();
  const { apiToken, setApiToken } = useCustomerApiToken();
  
  // Store hooks
  const { fetchInvoices, clearError } = useInvoiceStore();
  const { invoices, isLoading, error, pagination } = useInvoiceData();
  const { filters, setFilters } = useInvoiceFilters();
  const { 
    columnVisibility, 
    setColumnVisibility, 
    sortBy, 
    sortOrder, 
    setSorting 
  } = useInvoiceTable();
  const { setPagination } = useInvoicePagination();

  // Local state for API token input
  const [showApiTokenInput, setShowApiTokenInput] = useState(!apiToken);
  const [apiTokenInput, setApiTokenInput] = useState(apiToken);

  // Initialize filters from URL params on mount
  useEffect(() => {
    const urlFilters = InvoiceService.parseQueryParams(searchParams);
    if (Object.keys(urlFilters).length > 0) {
      setFilters(urlFilters);
    }
  }, [searchParams, setFilters]);

  // Auto-fetch invoices when filters change (if API token is available)
  useEffect(() => {
    if (apiToken && filters.startDate && filters.endDate) {
      handleSearch();
    }
  }, [apiToken]); // Only trigger on API token changes

  const handleFiltersChange = (newFilters: Partial<IInvoiceFilters>) => {
    setFilters(newFilters);
    
    // Update URL params
    const currentParams = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        currentParams.set(key, String(value));
      } else {
        currentParams.delete(key);
      }
    });
    setSearchParams(currentParams);
  };

  const handleSearch = async () => {
    if (!apiToken) {
      toast.error("API token is required", {
        description: "Please enter your API token to fetch invoices.",
      });
      setShowApiTokenInput(true);
      return;
    }

    if (!filters.startDate || !filters.endDate) {
      toast.error("Date range is required", {
        description: "Please select both start and end dates.",
      });
      return;
    }

    const request: GetInvoicesRequest = {
      apiToken,
      startDate: filters.startDate,
      endDate: filters.endDate,
      rowPerPage: pagination.pageSize,
      pageNum: pagination.page - 1, // Backend uses 0-based pagination
      ...Object.fromEntries(
        Object.entries(filters).filter(([key, value]) => 
          key !== 'startDate' && 
          key !== 'endDate' && 
          value !== undefined && 
          value !== null && 
          value !== ''
        )
      ),
    };

    await fetchInvoices(request);
  };

  const handlePageChange = (page: number) => {
    setPagination({ page });
    
    // Trigger search with new page
    if (apiToken && filters.startDate && filters.endDate) {
      const request: GetInvoicesRequest = {
        apiToken,
        startDate: filters.startDate,
        endDate: filters.endDate,
        rowPerPage: pagination.pageSize,
        pageNum: page - 1, // Backend uses 0-based pagination
        ...Object.fromEntries(
          Object.entries(filters).filter(([key, value]) => 
            key !== 'startDate' && 
            key !== 'endDate' && 
            value !== undefined && 
            value !== null && 
            value !== ''
          )
        ),
      };
      fetchInvoices(request);
    }
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPagination({ pageSize, page: 1 });
    
    // Trigger search with new page size
    if (apiToken && filters.startDate && filters.endDate) {
      const request: GetInvoicesRequest = {
        apiToken,
        startDate: filters.startDate,
        endDate: filters.endDate,
        rowPerPage: pageSize,
        pageNum: 0, // Reset to first page
        ...Object.fromEntries(
          Object.entries(filters).filter(([key, value]) => 
            key !== 'startDate' && 
            key !== 'endDate' && 
            value !== undefined && 
            value !== null && 
            value !== ''
          )
        ),
      };
      fetchInvoices(request);
    }
  };

  const handleSort = (column: string) => {
    const newOrder = sortBy === column && sortOrder === 'asc' ? 'desc' : 'asc';
    setSorting(column, newOrder);
  };

  const handleApiTokenSubmit = () => {
    if (!apiTokenInput.trim()) {
      toast.error("Please enter a valid API token");
      return;
    }
    
    setApiToken(apiTokenInput.trim());
    setShowApiTokenInput(false);
    toast.success("API token saved successfully");
  };

  const handleRefresh = () => {
    if (apiToken && filters.startDate && filters.endDate) {
      handleSearch();
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Invoices</h1>
          <p className="text-muted-foreground">
            View and manage your invoices from the Sinvoice system
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading || !apiToken}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* API Token Input */}
      {showApiTokenInput && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center gap-4">
            <span>Enter your API token to access invoices:</span>
            <div className="flex items-center gap-2">
              <input
                type="password"
                value={apiTokenInput}
                onChange={(e) => setApiTokenInput(e.target.value)}
                placeholder="Enter API token"
                className="px-3 py-1 border rounded text-sm"
                onKeyDown={(e) => e.key === 'Enter' && handleApiTokenSubmit()}
              />
              <Button size="sm" onClick={handleApiTokenSubmit}>
                Save
              </Button>
              {apiToken && (
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => setShowApiTokenInput(false)}
                >
                  Cancel
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <InvoiceFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onSearch={handleSearch}
        isLoading={isLoading}
      />

      {/* Invoice Table */}
      <InvoiceTable
        invoices={invoices}
        columns={DEFAULT_INVOICE_COLUMNS}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={setColumnVisibility}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSort={handleSort}
        isLoading={isLoading}
      />

      {/* Pagination */}
      {pagination.total > 0 && (
        <Pagination
          page={pagination.page}
          pageSize={pagination.pageSize}
          totalItems={pagination.total}
          totalPages={pagination.totalPages}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          showPageSize={true}
        />
      )}

      {/* Debug Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-8 p-4 bg-muted rounded-lg">
          <summary className="cursor-pointer font-medium">Debug Info</summary>
          <pre className="mt-2 text-xs overflow-auto">
            {JSON.stringify({
              filters,
              pagination,
              columnVisibility,
              sortBy,
              sortOrder,
              hasApiToken: !!apiToken,
              invoiceCount: invoices.length,
            }, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );
}
