# Application Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1
SINVOICE_API_URL=https://api-vinvoice.viettel.vn

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=invoice_user
DB_PASSWORD=invoice_password
DB_DATABASE=invoice_db

# Database SSL Configuration
# Enable SSL connection to database (required for production cloud databases)
DB_SSL_ENABLED=false

# SSL Certificate Validation
# Set to false for cloud providers with self-signed certificates (AWS RDS, etc.)
# Set to true for custom certificates or strict validation
DB_SSL_REJECT_UNAUTHORIZED=true

# SSL Certificate Paths (optional - for custom certificates)
# Path to CA certificate file (Certificate Authority)
# DB_SSL_CA_CERT_PATH=./certs/ca-certificate.crt

# Path to client certificate file (for mutual TLS authentication)
# DB_SSL_CLIENT_CERT_PATH=./certs/client-certificate.crt

# Path to client private key file (for mutual TLS authentication)
# DB_SSL_CLIENT_KEY_PATH=./certs/client-key.key

# SSL Mode (optional - PostgreSQL SSL modes)
# Options: disable, allow, prefer, require, verify-ca, verify-full
# DB_SSL_MODE=require

# Database Connection Pool Settings (optimized for SSL connections)
DB_POOL_MAX=10
DB_POOL_MIN=2
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# Encryption Configuration
# 32-character encryption key for AES-256-GCM (change in production)
ENCRYPTION_KEY=your-32-char-encryption-key-here

# Admin Account Configuration
# Default admin account credentials (change after first login!)
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123



# CORS Configuration
CORS_ORIGIN=http://localhost:3001

# Logging
LOG_LEVEL=debug

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_DEST=./uploads
