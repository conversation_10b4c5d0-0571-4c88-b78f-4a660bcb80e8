import { z } from "zod";

export const customerValidation = {
  username: z
    .string()
    .min(3, "Username must be at least 3 characters")
    .max(50, "Username must be less than 50 characters")
    .regex(
      /^[a-zA-Z0-9_-]+$/,
      "Username can only contain letters, numbers, underscores, and hyphens"
    ),
  email: z
    .string()
    .email("Invalid email address")
    .max(255, "Email must be less than 255 characters"),
  password: z
    .string()
    .min(6, "Password must be at least 6 characters")
    .max(255, "Password must be less than 255 characters"),
  sinvoiceUsername: z
    .string()
    .min(3, "Sinvoice username must be at least 3 characters")
    .max(255, "Sinvoice username must be less than 255 characters"),
  sinvoicePassword: z
    .string()
    .min(6, "Sinvoice password must be at least 6 characters")
    .max(255, "Sinvoice password must be less than 255 characters"),
  isActive: z.boolean(),
};

export const createCustomerSchema = z.object({
  username: customerValidation.username,
  email: customerValidation.email,
  password: customerValidation.password,
  sinvoiceUsername: customerValidation.sinvoiceUsername,
  sinvoicePassword: customerValidation.sinvoicePassword,
});

export const updateCustomerSchema = z
  .object({
    username: z.string().optional(),
    email: z.string().optional(),
    password: z.string().optional(),
    isActive: z.boolean().optional(),
    sinvoiceUsername: z.string().optional(),
    sinvoicePassword: z.string().optional(),
  })
  .partial();

export type CreateCustomerFormData = z.infer<typeof createCustomerSchema>;
export type UpdateCustomerFormData = z.infer<typeof updateCustomerSchema>;
