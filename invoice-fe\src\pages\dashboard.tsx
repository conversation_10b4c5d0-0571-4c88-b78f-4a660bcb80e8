import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { useAuthStore } from '../stores/auth.store';
import { UserRole } from '../types';
import { 
  FileText, 
  Users, 
  DollarSign, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

export function DashboardPage() {
  const { user } = useAuthStore();

  const isAdmin = user?.role === UserRole.ADMIN;

  const stats = [
    {
      title: 'Total Invoices',
      value: isAdmin ? '1,234' : '45',
      description: isAdmin ? 'All invoices in system' : 'Your invoices',
      icon: FileText,
      trend: '+12%',
    },
    {
      title: isAdmin ? 'Total Customers' : 'Pending Payments',
      value: isAdmin ? '89' : '3',
      description: isAdmin ? 'Active customers' : 'Awaiting payment',
      icon: isAdmin ? Users : Clock,
      trend: isAdmin ? '+5%' : '-2',
    },
    {
      title: 'Revenue',
      value: isAdmin ? '$45,231' : '$2,340',
      description: isAdmin ? 'Total revenue' : 'Your payments',
      icon: DollarSign,
      trend: '+23%',
    },
    {
      title: 'Growth',
      value: '+12.5%',
      description: 'From last month',
      icon: TrendingUp,
      trend: '+2.1%',
    },
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'invoice_created',
      title: 'Invoice #INV-001 created',
      description: 'New invoice for $1,250.00',
      time: '2 hours ago',
      status: 'pending',
    },
    {
      id: 2,
      type: 'payment_received',
      title: 'Payment received',
      description: 'Invoice #INV-098 paid - $850.00',
      time: '4 hours ago',
      status: 'completed',
    },
    {
      id: 3,
      type: 'invoice_overdue',
      title: 'Invoice overdue',
      description: 'Invoice #INV-095 is 5 days overdue',
      time: '1 day ago',
      status: 'overdue',
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'overdue':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'default',
      pending: 'secondary',
      overdue: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          Welcome back, {user?.username}!
        </h1>
        <p className="text-muted-foreground">
          {isAdmin 
            ? "Here's an overview of your invoice system." 
            : "Here's an overview of your invoices and payments."
          }
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                <span className={`${stat.trend.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                  {stat.trend}
                </span>{' '}
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest updates on your {isAdmin ? 'system' : 'account'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getStatusIcon(activity.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground">
                      {activity.title}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {activity.description}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(activity.status)}
                    <span className="text-xs text-muted-foreground">
                      {activity.time}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <button className="w-full text-left p-3 rounded-lg border hover:bg-accent transition-colors">
              <div className="font-medium">Create New Invoice</div>
              <div className="text-sm text-muted-foreground">Generate a new invoice</div>
            </button>
            {isAdmin && (
              <button className="w-full text-left p-3 rounded-lg border hover:bg-accent transition-colors">
                <div className="font-medium">Add Customer</div>
                <div className="text-sm text-muted-foreground">Register new customer</div>
              </button>
            )}
            <button className="w-full text-left p-3 rounded-lg border hover:bg-accent transition-colors">
              <div className="font-medium">View Reports</div>
              <div className="text-sm text-muted-foreground">Check analytics</div>
            </button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
