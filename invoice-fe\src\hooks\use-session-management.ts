import { useEffect, useRef } from 'react';
import { useAuthStore } from '../stores/auth.store';

interface UseSessionManagementOptions {
  warningTimeBeforeExpiry?: number; // in minutes
  checkInterval?: number; // in milliseconds
}

export function useSessionManagement(options: UseSessionManagementOptions = {}) {
  const {
    warningTimeBeforeExpiry = 5, // 5 minutes before expiry
    checkInterval = 60000, // Check every minute
  } = options;

  const { isAuthenticated, showSessionExpirationWarning, refreshToken } = useAuthStore();
  const warningShownRef = useRef(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      warningShownRef.current = false;
      return;
    }

    // Check session status periodically
    intervalRef.current = setInterval(() => {
      const token = localStorage.getItem('auth-token');
      if (!token) return;

      try {
        // In a real app, you would decode the JWT token to check expiry
        // For now, we'll simulate session management
        const tokenData = JSON.parse(atob(token.split('.')[1]));
        const expiryTime = tokenData.exp * 1000; // Convert to milliseconds
        const currentTime = Date.now();
        const timeUntilExpiry = expiryTime - currentTime;
        const warningTime = warningTimeBeforeExpiry * 60 * 1000; // Convert to milliseconds

        // Show warning if session will expire soon
        if (timeUntilExpiry <= warningTime && timeUntilExpiry > 0 && !warningShownRef.current) {
          showSessionExpirationWarning();
          warningShownRef.current = true;
        }

        // Auto-refresh token if it's about to expire (1 minute before)
        if (timeUntilExpiry <= 60000 && timeUntilExpiry > 0) {
          refreshToken().catch(() => {
            // Refresh failed, user will be logged out
          });
        }
      } catch (error) {
        // Invalid token format, ignore
        console.warn('Invalid token format:', error);
      }
    }, checkInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isAuthenticated, warningTimeBeforeExpiry, checkInterval, showSessionExpirationWarning, refreshToken]);

  // Reset warning flag when user becomes authenticated (after login)
  useEffect(() => {
    if (isAuthenticated) {
      warningShownRef.current = false;
    }
  }, [isAuthenticated]);
}
