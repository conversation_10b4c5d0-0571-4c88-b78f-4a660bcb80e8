# Invoice Service Backend

A robust and scalable invoice management system built with NestJS, TypeScript, and PostgreSQL with comprehensive SSL support.

## 🚀 Features

- **Modern Architecture**: Built with NestJS framework following industry best practices
- **Database Integration**: PostgreSQL with TypeORM and SSL support
- **JWT Authentication**: Secure JWT-based authentication with role-based access control
- **SSL Security**: Comprehensive SSL/TLS support for database connections
- **Cloud Ready**: Compatible with AWS RDS, Google Cloud SQL, Azure Database
- **API Documentation**: Swagger/OpenAPI integration for comprehensive API docs
- **Security**: Helmet, CORS, and input validation for secure operations
- **Testing**: Comprehensive unit and e2e testing setup
- **Docker Support**: Containerized development and deployment
- **Configuration Management**: Environment-based configuration with validation
- **Error Handling**: Global exception filters and structured error responses

## 🔐 SSL Database Support

The application supports secure SSL/TLS connections to PostgreSQL databases with:

- **Flexible SSL Configuration**: Optional for development, enforced for production
- **Cloud Provider Support**: Pre-configured for AWS RDS, Google Cloud SQL, Azure Database
- **Certificate Management**: Support for CA certificates, client certificates, and private keys
- **SSL Modes**: Full range of PostgreSQL SSL modes (disable, allow, prefer, require, verify-ca, verify-full)
- **Connection Pooling**: Optimized connection pools for SSL connections

### Quick SSL Setup

Use the automated SSL setup script:

```bash
npm run ssl:setup
```

This interactive script will guide you through setting up SSL for your database provider.

## 🔐 JWT Authentication

The application includes a comprehensive JWT-based authentication system with:

- **Secure Login**: JWT token-based authentication with bcrypt password hashing
- **Role-Based Access Control**: Admin and customer roles with permission management
- **Rate Limiting**: Login attempt rate limiting to prevent brute force attacks
- **Admin Account**: Automatic admin account creation via database seeder
- **Token Management**: Configurable JWT expiration and secure token validation

### Authentication Endpoints

- `POST /api/v1/auth/login` - User login (returns JWT token)
- `GET /api/v1/auth/profile` - Get current user profile (requires authentication)
- `POST /api/v1/auth/logout` - User logout

### Customer Management Endpoints

- `POST /api/v1/customers` - Create new customer account (admin only)
- `GET /api/v1/customers` - Get all customers (admin only)
- `GET /api/v1/customers/me` - Get current customer profile (customer only)
- `GET /api/v1/customers/:id` - Get customer by ID (admin only)
- `PUT /api/v1/customers/me` - Update current customer profile (customer only)
- `PUT /api/v1/customers/:id` - Update customer by ID (admin only)
- `POST /api/v1/customers/:id/reset-token` - Reset customer API token (admin only)

### Default Admin Account

The system automatically creates a default admin account on first setup:

- **Username**: `admin` (configurable via `ADMIN_USERNAME`)
- **Email**: `<EMAIL>` (configurable via `ADMIN_EMAIL`)
- **Password**: `admin123` (configurable via `ADMIN_PASSWORD`)

⚠️ **Important**: Change the default admin password after first login!

### Authentication Setup

1. **Configure JWT Secret** (required):

   ```bash
   JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
   JWT_EXPIRES_IN=24h
   ```

2. **Configure Admin Account** (optional):

   ```bash
   ADMIN_USERNAME=admin
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=secure-admin-password
   ```

3. **Configure Customer Encryption** (required for production):

   ```bash
   ENCRYPTION_KEY=your-super-secret-encryption-key-for-customer-data
   ```

4. **Run Database Setup** (creates admin account):
   ```bash
   npm run db:setup
   # or just seed the admin account
   npm run auth:seed
   ```

## 🏢 Customer Management

The application includes comprehensive customer account management with secure credential storage and API token authentication.

### Customer Features

- **Secure Account Creation**: Admin-controlled customer account creation with role-based access
- **Sinvoice Integration**: Encrypted storage of Sinvoice credentials for API integration
- **API Token Authentication**: Unique API tokens for customer service access
- **Session Management**: Automatic handling of Sinvoice access and refresh tokens
- **Token Reset**: Admin capability to reset customer API tokens
- **Encrypted Storage**: AES-256-GCM encryption for sensitive Sinvoice credentials

### Customer Data Security

- **Password Encryption**: Sinvoice passwords encrypted using AES-256-GCM
- **API Tokens**: Cryptographically secure 64-character hex tokens
- **Role-Based Access**: Strict separation between admin and customer operations
- **Session Tokens**: Secure storage and management of Sinvoice session tokens

### Using Authentication

1. **Login to get JWT token**:

   ```bash
   curl -X POST http://localhost:3000/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}'
   ```

2. **Use token in subsequent requests**:
   ```bash
   curl -X GET http://localhost:3000/api/v1/auth/profile \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

### Role-Based Access Control (RBAC)

The application implements a comprehensive RBAC system with the following roles:

#### Available Roles

- **ADMIN**: Full system access with all permissions
- **CUSTOMER**: Limited access (permissions defined per business requirements)

#### RBAC Components

1. **Role Enum**: Defined in `UserRole` enum
2. **Guards**: `JwtAuthGuard` for authentication, `RolesGuard` for authorization
3. **Decorators**: `@Roles()` for role-based route protection, `@Public()` for public routes
4. **Current User**: `@CurrentUser()` decorator to access authenticated user

### Protecting Routes

Use decorators to protect routes and control access:

```typescript
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { UserRole, User } from '../database/entities/user.entity';

// Require authentication
@UseGuards(JwtAuthGuard)
@Get('protected')
async protectedRoute() {
  return { message: 'This requires authentication' };
}

// Require admin role
@Roles(UserRole.ADMIN)
@Get('admin-only')
async adminOnlyRoute() {
  return { message: 'This requires admin role' };
}

// Require customer role
@Roles(UserRole.CUSTOMER)
@Get('customer-only')
async customerOnlyRoute() {
  return { message: 'This requires customer role' };
}

// Multiple roles allowed
@Roles(UserRole.ADMIN, UserRole.CUSTOMER)
@Get('admin-or-customer')
async adminOrCustomerRoute() {
  return { message: 'This requires admin or customer role' };
}

// Access current user
@Roles(UserRole.ADMIN)
@Get('current-user')
async getCurrentUser(@CurrentUser() user: User) {
  return { user: user.toJSON() };
}

// Public route (no authentication required)
@Public()
@Get('public')
async publicRoute() {
  return { message: 'This is public' };
}
```

#### Global Guards

The application uses global guards configured in `app.module.ts`:

- **JwtAuthGuard**: Automatically applied to all routes (except `@Public()`)
- **RolesGuard**: Automatically checks role requirements when `@Roles()` is used

### Manual SSL Configuration

#### For Cloud Providers (AWS RDS, Google Cloud SQL, Azure)

1. **AWS RDS**:

   ```bash
   # Download AWS RDS CA certificate
   curl -o certs/ca-certificate.crt https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem
   ```

2. **Environment Variables**:
   ```bash
   DB_SSL_ENABLED=true
   DB_SSL_REJECT_UNAUTHORIZED=false
   DB_SSL_CA_CERT_PATH=./certs/ca-certificate.crt
   DB_SSL_MODE=require
   DB_CLOUD_PROVIDER=aws
   ```

#### For Custom SSL Certificates

1. **Place certificates in the `certs/` directory**:

   - `ca-certificate.crt` - Certificate Authority certificate
   - `client-certificate.crt` - Client certificate (optional)
   - `client-key.key` - Client private key (optional)

2. **Set proper permissions**:

   ```bash
   chmod 644 certs/ca-certificate.crt
   chmod 644 certs/client-certificate.crt
   chmod 600 certs/client-key.key
   ```

3. **Environment Variables**:
   ```bash
   DB_SSL_ENABLED=true
   DB_SSL_REJECT_UNAUTHORIZED=true
   DB_SSL_CA_CERT_PATH=./certs/ca-certificate.crt
   DB_SSL_CLIENT_CERT_PATH=./certs/client-certificate.crt
   DB_SSL_CLIENT_KEY_PATH=./certs/client-key.key
   DB_SSL_MODE=verify-full
   ```

## 📁 Project Structure

```
src/
├── common/                 # Shared utilities and components
│   ├── constants/         # Application constants and enums
│   ├── decorators/        # Custom decorators
│   ├── dto/              # Shared Data Transfer Objects
│   ├── filters/          # Exception filters
│   ├── guards/           # Authentication and authorization guards
│   ├── interceptors/     # Request/response interceptors
│   ├── middleware/       # Custom middleware
│   ├── pipes/            # Validation and transformation pipes
│   └── utils/            # Utility functions (including SSL utilities)
├── config/                # Configuration files
│   ├── app.config.ts     # Application configuration
│   ├── database.config.ts # Database configuration with SSL support
│   ├── jwt.config.ts     # JWT configuration
│   └── validation.schema.ts # Environment validation with SSL variables
├── database/              # Database related files
│   ├── entities/         # TypeORM entities
│   ├── migrations/       # Database migrations
│   ├── repositories/     # Custom repositories
│   └── seeds/            # Database seed files
├── modules/               # Feature modules
│   ├── auth/             # Authentication module
│   ├── customers/        # Customer management
│   └── invoices/         # Invoice management
└── main.ts               # Application entry point
```

## 🛠️ Prerequisites

- Node.js (>= 20.0.0)
- npm (>= 10.0.0)
- PostgreSQL (>= 13) with SSL support
- Docker & Docker Compose (optional)

## 🚀 Quick Start

### 1. Clone and Install Dependencies

```bash
# Install dependencies
npm install
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
```

### 3. SSL Setup (if using cloud database)

```bash
# Run interactive SSL setup
npm run ssl:setup

# Or manually configure SSL in .env file
```

### 4. Database Setup

#### Option A: Using Docker (Local Development)

```bash
# Start PostgreSQL with Docker (SSL disabled)
npm run docker:up

# Run migrations and seeds
npm run db:setup
```

#### Option B: Cloud Database with SSL

```bash
# Configure SSL in .env file first
# Then run migrations
npm run migration:run

# Run seeds (optional)
npm run seed:run
```

### 5. Start Development Server

```bash
# Start in development mode
npm run start:dev
```

The API will be available at:

- **API**: http://localhost:3000/api/v1
- **Swagger Docs**: http://localhost:3000/api/v1/docs

## 📝 Available Scripts

### Development

```bash
npm run start:dev          # Start development server with hot reload
npm run start:debug        # Start with debugging enabled
npm run build              # Build for production
npm run start:prod         # Start production server
```

### Database

```bash
npm run migration:generate # Generate new migration
npm run migration:run      # Run pending migrations
npm run migration:revert   # Revert last migration
npm run seed:run           # Run database seeds
npm run auth:seed          # Create admin user account
npm run db:reset           # Reset database and create admin
```

### SSL Management

```bash
npm run ssl:setup          # Interactive SSL setup wizard
npm run ssl:test           # Test SSL configuration
```

### Testing

```bash
npm run test               # Run unit tests
npm run test:watch         # Run tests in watch mode
npm run test:cov           # Run tests with coverage
npm run test:e2e           # Run e2e tests
```

### Docker

```bash
npm run docker:up          # Start development services
npm run docker:down        # Stop development services
npm run docker:prod        # Start production services
npm run docker:prod:down   # Stop production services
npm run docker:logs        # View logs
npm run docker:build       # Build images
```

## 🔧 Configuration

### Environment Variables

The application uses environment-based configuration with comprehensive SSL support:

#### Database Configuration

- `DB_HOST`: Database host
- `DB_PORT`: Database port (default: 5432)
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password
- `DB_DATABASE`: Database name

#### SSL Configuration

- `DB_SSL_ENABLED`: Enable SSL connection (true/false)
- `DB_SSL_REJECT_UNAUTHORIZED`: Validate SSL certificates (true/false)
- `DB_SSL_CA_CERT_PATH`: Path to CA certificate file
- `DB_SSL_CLIENT_CERT_PATH`: Path to client certificate file (optional)
- `DB_SSL_CLIENT_KEY_PATH`: Path to client private key file (optional)
- `DB_SSL_MODE`: PostgreSQL SSL mode (disable, allow, prefer, require, verify-ca, verify-full)
- `DB_CLOUD_PROVIDER`: Cloud provider (aws, gcp, azure, custom)

#### Connection Pool Settings

- `DB_POOL_MAX`: Maximum connections (default: 10)
- `DB_POOL_MIN`: Minimum connections (default: 2)
- `DB_POOL_ACQUIRE`: Connection acquisition timeout (default: 30000ms)
- `DB_POOL_IDLE`: Connection idle timeout (default: 10000ms)

#### Application Configuration

- `NODE_ENV`: Application environment (development/production/test)
- `PORT`: Server port (default: 3000)
- `JWT_SECRET`: JWT signing secret
- `CORS_ORIGIN`: Allowed CORS origins

## 🧪 Testing

The project includes comprehensive testing setup with SSL support:

- **Unit Tests**: Located alongside source files with `.spec.ts` extension
- **Integration Tests**: Located in `test/integration/`
- **E2E Tests**: Located in `test/` directory with SSL configuration testing
- **Test Fixtures**: Shared test data in `test/fixtures/`

### Testing SSL Configuration

```bash
# Test SSL configuration without starting the full application
npm run ssl:test

# Run tests with SSL-enabled database
DB_SSL_ENABLED=true npm run test:e2e
```

## 📚 API Documentation

When running in development mode, Swagger documentation is available at:
`http://localhost:3000/api/v1/docs`

## 🐳 Docker Development

### Development Environment (SSL Disabled)

```bash
# Start all services (PostgreSQL, Redis, App)
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Environment (SSL Enabled)

```bash
# Configure SSL environment variables first
# Then start production services
docker-compose -f docker-compose.prod.yml up -d

# Stop production services
docker-compose -f docker-compose.prod.yml down
```

## 🔍 SSL Troubleshooting

### Common SSL Issues

1. **Certificate not found**:

   ```
   Error: ENOENT: no such file or directory, open './certs/ca-certificate.crt'
   ```

   - Solution: Verify certificate file path and permissions

2. **SSL handshake failed**:

   ```
   Error: SSL connection failed
   ```

   - Solution: Check certificate validity and SSL mode configuration

3. **Connection timeout**:

   ```
   Error: Connection timeout
   ```

   - Solution: Verify network connectivity and firewall rules

4. **Certificate verification failed**:
   ```
   Error: unable to verify the first certificate
   ```
   - Solution: Set `DB_SSL_REJECT_UNAUTHORIZED=false` for cloud providers

### Debug SSL Configuration

Enable debug logging to troubleshoot SSL issues:

```bash
LOG_LEVEL=debug npm run start:dev
```

The application will log detailed SSL configuration information during startup.

## 🏗️ Architecture

The application follows NestJS best practices with enhanced SSL security:

- **Modular Architecture**: Feature-based modules with clear boundaries
- **Dependency Injection**: Leveraging NestJS DI container
- **Separation of Concerns**: Controllers, Services, Repositories pattern
- **Configuration Management**: Environment-based configuration with SSL validation
- **Error Handling**: Global exception filters with SSL error handling
- **Validation**: Class-validator for request validation
- **Documentation**: Swagger/OpenAPI integration
- **SSL Security**: Comprehensive SSL/TLS support for database connections

## 🔒 Security Best Practices

### SSL Certificate Management

1. **Never commit certificates to version control**
2. **Use restrictive file permissions** (600 for private keys, 644 for certificates)
3. **Regularly rotate certificates** before expiration
4. **Use secure certificate storage** in production (AWS Secrets Manager, Azure Key Vault, etc.)
5. **Validate certificate chains** in production environments

### Production Deployment

1. **Enable SSL** for all database connections
2. **Use certificate validation** (`DB_SSL_REJECT_UNAUTHORIZED=true`) when possible
3. **Configure proper SSL modes** (`verify-full` for maximum security)
4. **Monitor certificate expiration** dates
5. **Use connection pooling** for better performance with SSL

## 📄 License

This project is [MIT licensed](LICENSE).

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support and questions:

- Create an issue in the repository
- Check the SSL troubleshooting section
- Review the API documentation at `/api/v1/docs`
- Consult the SSL setup guide in `certs/README.md`
