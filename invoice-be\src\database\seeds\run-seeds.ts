/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { AdminUserSeeder } from './admin-user.seeder';
import * as fs from 'fs';

// Load environment variables
config();

async function runSeeds() {
  console.log('🌱 Starting database seeding...');

  // Create TypeORM data source
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'invoice_user',
    password: process.env.DB_PASSWORD || 'invoice_password',
    database: process.env.DB_DATABASE || 'invoice_db',
    entities: [__dirname + '/../entities/*.entity{.ts,.js}'],
    synchronize: false,
    logging: false,
    ssl:
      process.env.DB_SSL_ENABLED === 'true'
        ? {
            rejectUnauthorized:
              process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
            ca: process.env.DB_SSL_CA_CERT_PATH
              ? fs.readFileSync(process.env.DB_SSL_CA_CERT_PATH)
              : undefined,
          }
        : false,
  });

  try {
    // Initialize data source
    await dataSource.initialize();
    console.log('📦 Database connection established');

    // Run seeders
    const adminSeeder = new AdminUserSeeder();
    await adminSeeder.run(dataSource);

    console.log('✅ Database seeding completed successfully');
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  } finally {
    // Close connection
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('📦 Database connection closed');
    }
  }
}

// Run seeds if this file is executed directly
if (require.main === module) {
  runSeeds();
}

export { runSeeds };
