import { User, UserRole } from "./auth";

export interface CustomerUser {
  id: string;
  username: string;
  email: string;
  role: User<PERSON><PERSON>;
  isActive: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Customer {
  id: string;
  user: CustomerUser;
  sinvoiceUsername: string;
  apiToken: string;
  hasSinvoiceTokens: boolean;
  sinvoiceTokenExpiresAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateCustomerRequest {
  username: string;
  email: string;
  password: string;
  sinvoiceUsername: string;
  sinvoicePassword: string;
}

export interface UpdateCustomerRequest {
  username?: string;
  email?: string;
  password?: string;
  isActive?: boolean;
  sinvoiceUsername?: string;
  sinvoicePassword?: string;
}

export interface ResetApiTokenRequest {
  confirmation: string;
}

export interface ApiTokenResponse {
  apiToken: string;
  message: string;
}

export interface CustomerFilters {
  search?: string;
  isActive?: boolean;
  hasSinvoiceTokens?: boolean;
}

export interface CustomerState {
  customers: Customer[];
  selectedCustomer: Customer | null;
  isLoading: boolean;
  error: string | null;
  filters: CustomerFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CustomerActions {
  // Data fetching
  fetchCustomers: (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }) => Promise<void>;
  fetchCustomerById: (id: string) => Promise<Customer>;

  // CRUD operations
  createCustomer: (data: CreateCustomerRequest) => Promise<Customer>;
  updateCustomer: (
    id: string,
    data: UpdateCustomerRequest
  ) => Promise<Customer>;
  deleteCustomer: (id: string) => Promise<void>;
  resetApiToken: (id: string) => Promise<ApiTokenResponse>;

  // State management
  setSelectedCustomer: (customer: Customer | null) => void;
  setFilters: (filters: Partial<CustomerFilters>) => void;
  clearError: () => void;

  // Pagination
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}

export type CustomerStore = CustomerState & CustomerActions;
