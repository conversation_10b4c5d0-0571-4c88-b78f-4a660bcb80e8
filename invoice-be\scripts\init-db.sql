-- Initialize database for invoice service
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create additional schemas if needed
-- CREATE SCHEMA IF NOT EXISTS audit;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE invoice_db TO invoice_user;

-- Create initial tables will be handled by TypeORM migrations
-- User authentication tables will be created via migrations
-- Admin user will be seeded via the seeder script
