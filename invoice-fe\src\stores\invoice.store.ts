import { create } from "zustand";
import { toast } from "sonner";
import { InvoiceService } from "../services/invoice.service";
import {
  InvoiceStore,
  Invoice,
  GetInvoicesRequest,
  InvoiceFilters,
  InvoicePagination,
  ColumnVisibility,
  DEFAULT_COLUMN_VISIBILITY,
} from "../types";

// Local storage keys
const COLUMN_VISIBILITY_KEY = "invoice-column-visibility";
const FILTERS_KEY = "invoice-filters";

// Helper function to load from localStorage
const loadFromStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch {
    return defaultValue;
  }
};

// Helper function to save to localStorage
const saveToStorage = <T>(key: string, value: T): void => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.warn(`Failed to save ${key} to localStorage:`, error);
  }
};

export const useInvoiceStore = create<InvoiceStore>((set, get) => ({
  // Initial state
  invoices: [],
  isLoading: false,
  error: null,
  filters: loadFromStorage(FILTERS_KEY, InvoiceService.getDefaultFilters()),
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  },
  columnVisibility: loadFromStorage(COLUMN_VISIBILITY_KEY, DEFAULT_COLUMN_VISIBILITY),
  sortBy: undefined,
  sortOrder: undefined,

  // Actions
  fetchInvoices: async (request: GetInvoicesRequest) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await InvoiceService.getInvoicesWithValidation(request);
      
      if (response.status === 'success' && response.data) {
        const formattedInvoices = InvoiceService.formatInvoices(response.data.invoices || []);
        
        set({
          invoices: formattedInvoices,
          isLoading: false,
          pagination: {
            page: response.data.pageNum + 1, // Backend uses 0-based, frontend uses 1-based
            pageSize: response.data.rowPerPage,
            total: response.data.totalRecords,
            totalPages: Math.ceil(response.data.totalRecords / response.data.rowPerPage),
          },
        });

        // Save successful filters to localStorage
        const { apiToken, ...filtersToSave } = request;
        saveToStorage(FILTERS_KEY, filtersToSave);
        
      } else {
        throw new Error(response.message || 'Failed to fetch invoices');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to fetch invoices';
      set({ 
        isLoading: false, 
        error: errorMessage,
        invoices: [],
        pagination: {
          page: 1,
          pageSize: request.rowPerPage || 10,
          total: 0,
          totalPages: 0,
        },
      });
      
      toast.error("Failed to fetch invoices", {
        description: errorMessage,
      });
    }
  },

  setFilters: (newFilters: Partial<InvoiceFilters>) => {
    const currentFilters = get().filters;
    const updatedFilters = { ...currentFilters, ...newFilters };
    
    set({ filters: updatedFilters });
    
    // Save filters to localStorage (excluding apiToken for security)
    const { apiToken, ...filtersToSave } = updatedFilters;
    saveToStorage(FILTERS_KEY, filtersToSave);
  },

  setPagination: (newPagination: Partial<InvoicePagination>) => {
    const currentPagination = get().pagination;
    const updatedPagination = { ...currentPagination, ...newPagination };
    
    set({ pagination: updatedPagination });
  },

  setColumnVisibility: (visibility: ColumnVisibility) => {
    set({ columnVisibility: visibility });
    saveToStorage(COLUMN_VISIBILITY_KEY, visibility);
  },

  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => {
    set({ sortBy, sortOrder });
    
    // Re-sort current invoices
    const { invoices } = get();
    const sortedInvoices = [...invoices].sort((a, b) => {
      const aValue = (a as any)[sortBy];
      const bValue = (b as any)[sortBy];
      
      if (aValue === bValue) return 0;
      
      let comparison = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else if (aValue instanceof Date && bValue instanceof Date) {
        comparison = aValue.getTime() - bValue.getTime();
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });
    
    set({ invoices: sortedInvoices });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set({
      invoices: [],
      isLoading: false,
      error: null,
      filters: InvoiceService.getDefaultFilters(),
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
      },
      sortBy: undefined,
      sortOrder: undefined,
    });
    
    // Clear localStorage
    localStorage.removeItem(FILTERS_KEY);
  },
}));

// Selector hooks for better performance
export const useInvoiceData = () => {
  const store = useInvoiceStore();
  return {
    invoices: store.invoices,
    isLoading: store.isLoading,
    error: store.error,
    pagination: store.pagination,
  };
};

export const useInvoiceFilters = () => {
  const store = useInvoiceStore();
  return {
    filters: store.filters,
    setFilters: store.setFilters,
  };
};

export const useInvoiceTable = () => {
  const store = useInvoiceStore();
  return {
    invoices: store.invoices,
    columnVisibility: store.columnVisibility,
    setColumnVisibility: store.setColumnVisibility,
    sortBy: store.sortBy,
    sortOrder: store.sortOrder,
    setSorting: store.setSorting,
  };
};

export const useInvoicePagination = () => {
  const store = useInvoiceStore();
  return {
    pagination: store.pagination,
    setPagination: store.setPagination,
  };
};

// Helper hook to get current customer's API token
export const useCustomerApiToken = () => {
  // This would typically come from the auth store or customer store
  // For now, we'll return a placeholder that needs to be implemented
  return {
    apiToken: localStorage.getItem('customer-api-token') || '',
    setApiToken: (token: string) => localStorage.setItem('customer-api-token', token),
  };
};
