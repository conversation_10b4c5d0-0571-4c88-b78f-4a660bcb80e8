import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserRoleToCustomer1749019096820
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, remove the default value to avoid casting issues
    await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role DROP DEFAULT
        `);

    // Update the enum type to include both 'user' and 'customer'
    await queryRunner.query(`
            ALTER TYPE users_role_enum RENAME TO users_role_enum_old
        `);

    await queryRunner.query(`
            CREATE TYPE users_role_enum AS ENUM ('admin', 'user', 'customer')
        `);

    await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role TYPE users_role_enum
            USING role::text::users_role_enum
        `);

    await queryRunner.query(`
            DROP TYPE users_role_enum_old
        `);

    // Now update existing 'user' role values to 'customer'
    await queryRunner.query(`
            UPDATE users
            SET role = 'customer'
            WHERE role = 'user'
        `);

    // Finally, remove 'user' from the enum type
    await queryRunner.query(`
            ALTER TYPE users_role_enum RENAME TO users_role_enum_old
        `);

    await queryRunner.query(`
            CREATE TYPE users_role_enum AS ENUM ('admin', 'customer')
        `);

    await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role TYPE users_role_enum
            USING role::text::users_role_enum
        `);

    await queryRunner.query(`
            DROP TYPE users_role_enum_old
        `);

    // Set the new default value to 'customer'
    await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role SET DEFAULT 'customer'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert 'customer' role values back to 'user'
    await queryRunner.query(`
            UPDATE users
            SET role = 'user'
            WHERE role = 'customer'
        `);

    // Revert the enum type to replace 'customer' with 'user'
    await queryRunner.query(`
            ALTER TYPE users_role_enum RENAME TO users_role_enum_old
        `);

    await queryRunner.query(`
            CREATE TYPE users_role_enum AS ENUM ('admin', 'user')
        `);

    await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role TYPE users_role_enum
            USING role::text::users_role_enum
        `);

    await queryRunner.query(`
            DROP TYPE users_role_enum_old
        `);
  }
}
