import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

config();

/**
 * Creates SSL configuration for TypeORM DataSource
 */
function createTypeOrmSSLConfig() {
  const sslEnabled = process.env.DB_SSL_ENABLED === 'true';

  if (!sslEnabled) {
    return false;
  }

  const sslConfig: any = {
    rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
  };

  // Add SSL certificate files if provided
  if (process.env.DB_SSL_CA_CERT_PATH) {
    const caPath = path.resolve(process.env.DB_SSL_CA_CERT_PATH);
    if (fs.existsSync(caPath)) {
      sslConfig.ca = fs.readFileSync(caPath);
    }
  }

  if (process.env.DB_SSL_CLIENT_CERT_PATH) {
    const certPath = path.resolve(process.env.DB_SSL_CLIENT_CERT_PATH);
    if (fs.existsSync(certPath)) {
      sslConfig.cert = fs.readFileSync(certPath);
    }
  }

  if (process.env.DB_SSL_CLIENT_KEY_PATH) {
    const keyPath = path.resolve(process.env.DB_SSL_CLIENT_KEY_PATH);
    if (fs.existsSync(keyPath)) {
      sslConfig.key = fs.readFileSync(keyPath);
    }
  }

  // Handle SSL mode
  if (process.env.DB_SSL_MODE) {
    sslConfig.sslmode = process.env.DB_SSL_MODE;
  }

  return sslConfig;
}

export default new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  username: process.env.DB_USERNAME || 'invoice_user',
  password: process.env.DB_PASSWORD || 'invoice_password',
  database: process.env.DB_DATABASE || 'invoice_db',
  entities: ['src/database/entities/*.entity.ts'],
  migrations: ['src/database/migrations/*.ts'],
  synchronize: false,
  logging: process.env.NODE_ENV === 'development',
  ssl: createTypeOrmSSLConfig(),
});
