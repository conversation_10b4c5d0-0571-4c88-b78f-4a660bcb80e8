{"name": "invoice-service-backend", "private": true, "version": "1.0.0", "description": "Invoice Service Backend API built with NestJS and PostgreSQL", "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/jest/bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:e2e:cov": "jest --config ./test/jest-e2e.json --coverage", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d typeorm.config.ts", "migration:run": "npm run typeorm -- migration:run -d typeorm.config.ts", "migration:revert": "npm run typeorm -- migration:revert -d typeorm.config.ts", "migration:create": "npm run typeorm -- migration:create", "schema:sync": "npm run typeorm -- schema:sync -d typeorm.config.ts", "schema:drop": "npm run typeorm -- schema:drop -d typeorm.config.ts", "seed:run": "ts-node src/database/seeds/run-seeds.ts", "auth:seed": "ts-node src/database/seeds/run-seeds.ts", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:build": "docker-compose build", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:prod:down": "docker-compose -f docker-compose.prod.yml down", "db:setup": "npm run docker:up && sleep 5 && npm run migration:run && npm run auth:seed", "db:reset": "npm run schema:drop && npm run migration:run && npm run auth:seed", "ssl:setup": "chmod +x scripts/setup-ssl.sh && ./scripts/setup-ssl.sh", "ssl:test": "node -e \"console.log('Testing SSL configuration...'); require('./dist/config/database.config.js')\""}, "engines": {"npm": ">=10.0.0", "node": ">=20.0.0"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^3.2.0", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^8.0.0", "@nestjs/throttler": "^6.2.1", "@nestjs/typeorm": "^10.0.2", "axios": "^1.7.9", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.4", "helmet": "^8.0.0", "joi": "^17.13.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.8", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@swc/core"]}}