import api from "../lib/api";
import {
  Customer,
  CreateCustomerRequest,
  UpdateCustomerRequest,
  ResetApiTokenRequest,
  ApiTokenResponse,
  QueryParams,
} from "../types";

export class CustomerService {
  private static readonly BASE_PATH = "/customers";

  /**
   * Get all customers (admin only)
   */
  static async getCustomers(params?: QueryParams): Promise<Customer[]> {
    const response = await api.get<Customer[]>(this.BASE_PATH, { params });
    return response.data;
  }

  /**
   * Get customer by ID (admin only)
   */
  static async getCustomerById(id: string): Promise<Customer> {
    const response = await api.get<Customer>(`${this.BASE_PATH}/${id}`);
    return response.data;
  }

  /**
   * Get current customer profile (customer only)
   */
  static async getCurrentCustomer(): Promise<Customer> {
    const response = await api.get<Customer>(`${this.BASE_PATH}/me`);
    return response.data;
  }

  /**
   * Create new customer account (admin only)
   */
  static async createCustomer(data: CreateCustomerRequest): Promise<Customer> {
    const response = await api.post<Customer>(this.BASE_PATH, data);
    return response.data;
  }

  /**
   * Update customer by ID (admin only)
   */
  static async updateCustomer(
    id: string,
    data: UpdateCustomerRequest
  ): Promise<Customer> {
    const response = await api.put<Customer>(`${this.BASE_PATH}/${id}`, data);
    return response.data;
  }

  /**
   * Update current customer profile (customer only)
   */
  static async updateCurrentCustomer(
    data: UpdateCustomerRequest
  ): Promise<Customer> {
    const response = await api.put<Customer>(`${this.BASE_PATH}/me`, data);
    return response.data;
  }

  /**
   * Reset customer API token (admin only)
   */
  static async resetApiToken(
    id: string,
    confirmation: ResetApiTokenRequest
  ): Promise<ApiTokenResponse> {
    const response = await api.post<ApiTokenResponse>(
      `${this.BASE_PATH}/${id}/reset-token`,
      confirmation
    );
    return response.data;
  }

  /**
   * Delete customer (admin only)
   */
  static async deleteCustomer(id: string): Promise<void> {
    await api.delete(`${this.BASE_PATH}/${id}`);
  }

  /**
   * Search customers with filters
   */
  static async searchCustomers(params: {
    search?: string;
    page?: number;
    limit?: number;
    isActive?: boolean;
    hasSinvoiceTokens?: boolean;
  }): Promise<Customer[]> {
    const queryParams: QueryParams = {
      page: params.page || 1,
      limit: params.limit || 10,
      search: params.search,
    };

    // Add custom filters as query parameters
    const customParams: Record<string, any> = { ...queryParams };
    if (params.isActive !== undefined) {
      customParams.isActive = params.isActive;
    }
    if (params.hasSinvoiceTokens !== undefined) {
      customParams.hasSinvoiceTokens = params.hasSinvoiceTokens;
    }

    const response = await api.get<Customer[]>(this.BASE_PATH, {
      params: customParams,
    });
    return response.data;
  }
}
