import {
  Injectable,
  Logger,
  UnauthorizedException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { GetInvoicesDto } from '../dto/get-invoices.dto';
import { CustomerService } from '../../customers/services/customer.service';

interface SinvoiceErrorResponse {
  code: number;
  message: string;
  data?: string;
  errorCode: string | null;
}

type GetInvoicesSearchParams = Omit<GetInvoicesDto, 'apiToken'>;

@Injectable()
export class InvoiceService {
  private readonly logger = new Logger(InvoiceService.name);
  private readonly sinvoiceApiUrl: string;

  // Sinvoice error code mappings
  private readonly errorMessages = {
    VAT_AMOUNT_INVALID:
      'Invalid VAT amount. Please check the tax calculations.',
    VAT_TAX_AMOUNT_NEGATE: 'VAT tax amount cannot be negative.',
    VAT_PERCENTAGE_INVALID:
      'Invalid VAT percentage. Please check the tax rate.',
    INVOICE_SERIAL_NOT_FOUND:
      'Invalid invoice serial number. Please check the invoice series.',
    IVI_TOTAL_A_WITHOUT_TAX_AND_UP_QUAN_NOT_COMPARED:
      'Unit price and total amount mismatch. Please check the calculations.',
    JSON_PARSE_ERROR: 'Invalid data format. Please check the input data.',
    BAD_REQUEST_INVOICE_NOT_USE_OTHER_FEE:
      'Other fees are not allowed for this invoice type.',
    BAD_REQUEST_INVALID_DECIMAL_POINT_QUANTUM:
      'Invalid decimal point configuration.',
    BAD_REQUEST_EXISTS_OTHER_USB_SIGN_PROCESSING:
      'There are unsigned invoices being processed. Please wait.',
    GENERAL: 'Session expired. Please login again.',
    DEFAULT:
      'An error occurred while processing the invoice. Please try again.',
  };

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly customerService: CustomerService,
  ) {
    this.sinvoiceApiUrl = this.configService.get<string>('SINVOICE_API_URL');

    if (!this.sinvoiceApiUrl) {
      throw new Error('SINVOICE_API_URL is not configured');
    }
  }

  /**
   * Handle Sinvoice API error responses
   */
  private handleSinvoiceError(error: AxiosError<SinvoiceErrorResponse>): never {
    const errorResponse = error.response?.data;
    const statusCode = error.response?.status;

    // Log detailed error for debugging
    this.logger.error('Sinvoice API Error Details', {
      statusCode,
      errorResponse,
      originalError: error.message,
    });

    // Handle specific error cases
    if (statusCode === 400) {
      const errorCode = errorResponse?.message || 'DEFAULT';
      const errorMessage =
        this.errorMessages[errorCode] || this.errorMessages.DEFAULT;
      const detailMessage = errorResponse?.data;

      throw new BadRequestException({
        statusCode: 400,
        message: errorMessage,
        error: 'Bad Request',
        details: detailMessage,
        code: errorCode,
      });
    }

    if (statusCode === 401) {
      throw new UnauthorizedException({
        statusCode: 401,
        message: 'Invalid Sinvoice credentials',
        error: 'Unauthorized',
      });
    }

    if (statusCode === 500) {
      // Check for token expiration
      if (errorResponse?.message === 'GENERAL') {
        throw new UnauthorizedException({
          statusCode: 401,
          message: this.errorMessages.GENERAL,
          error: 'Session Expired',
        });
      }

      throw new InternalServerErrorException({
        statusCode: 500,
        message: 'Sinvoice service is currently unavailable',
        error: 'Internal Server Error',
        details: errorResponse?.message,
      });
    }

    // Default error case
    throw new InternalServerErrorException({
      statusCode: 500,
      message: this.errorMessages.DEFAULT,
      error: 'Internal Server Error',
    });
  }

  /**
   * Create an invoice using Sinvoice API
   */
  async createInvoice(
    apiToken: string,
    createInvoiceDto: CreateInvoiceDto,
  ): Promise<any> {
    // Get customer by API token
    const customer = await this.customerService.getCustomerByApiToken(apiToken);
    if (!customer) {
      throw new UnauthorizedException('Invalid API token');
    }

    try {
      // Get decrypted Sinvoice credentials
      const sinvoiceUsername = customer.sinvoiceUsername;
      const sinvoicePassword = customer.getSinvoicePassword();

      // Create Basic Auth header
      const authHeader = Buffer.from(
        `${sinvoiceUsername}:${sinvoicePassword}`,
      ).toString('base64');

      // Log request details (excluding sensitive data)
      // this.logger.log(
      //   `Sending invoice creation request to Sinvoice for customer: ${customer.user.username}`,
      //   {
      //     endpoint: `${this.sinvoiceApiUrl}/services/einvoiceapplication/api/InvoiceAPI/InvoiceWS/createInvoice/${sinvoiceUsername}`,
      //     requestData: {
      //       generalInvoiceInfo: createInvoiceDto.generalInvoiceInfo,
      //       buyerInfo: {
      //         ...createInvoiceDto.buyerInfo,
      //         buyerBankAccount: '***hidden***',
      //         buyerIdNo: '***hidden***',
      //       },
      //       itemInfo: createInvoiceDto.itemInfo.map((item) => ({
      //         lineNumber: item.lineNumber,
      //         itemCode: item.itemCode,
      //         itemName: item.itemName,
      //         quantity: item.quantity,
      //         unitPrice: item.unitPrice,
      //       })),
      //     },
      //   },
      // );

      // Make request to Sinvoice API
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.sinvoiceApiUrl}/services/einvoiceapplication/api/InvoiceAPI/InvoiceWS/createInvoice/${sinvoiceUsername}`,
          createInvoiceDto,
          {
            headers: {
              Authorization: `Basic ${authHeader}`,
              'Content-Type': 'application/json',
              'User-Agent': 'Invoice-Service/1.0',
            },
            timeout: 30000, // 30 seconds timeout
          },
        ),
      );

      // Log successful response
      // this.logger.log(
      //   `Invoice created successfully for customer: ${customer.user.username}`,
      //   {
      //     statusCode: response.status,
      //     statusText: response.statusText,
      //     responseData: {
      //       ...response.data,
      //       // Mask any sensitive data in response if present
      //       customerInfo: response.data?.customerInfo
      //         ? {
      //             ...response.data.customerInfo,
      //             bankAccount: '***hidden***',
      //             idNo: '***hidden***',
      //           }
      //         : undefined,
      //     },
      //     headers: {
      //       ...response.headers,
      //       authorization: undefined, // Remove auth header from logs
      //     },
      //   },
      // );

      return response.data;
    } catch (error) {
      // Enhanced error logging
      if (error instanceof AxiosError) {
        // this.logger.error(
        //   `Failed to create invoice for customer: ${customer.user.username}`,
        //   {
        //     error: {
        //       message: error.message,
        //       code: error.code,
        //       status: error.response?.status,
        //       statusText: error.response?.statusText,
        //       data: error.response?.data,
        //       headers: {
        //         ...error.response?.headers,
        //         authorization: undefined, // Remove auth header from logs
        //       },
        //     },
        //     request: {
        //       method: error.config?.method?.toUpperCase(),
        //       url: error.config?.url,
        //       headers: {
        //         ...error.config?.headers,
        //         authorization: undefined, // Remove auth header from logs
        //       },
        //     },
        //   },
        // );

        // Handle Sinvoice specific errors
        return this.handleSinvoiceError(error);
      }

      // Log unexpected errors
      this.logger.error(
        `Unexpected error creating invoice for customer: ${customer.user.username}`,
        {
          error: error instanceof Error ? error.stack : error,
        },
      );

      throw new InternalServerErrorException({
        statusCode: 500,
        message: this.errorMessages.DEFAULT,
        error: 'Internal Server Error',
      });
    }
  }

  /**
   * Get invoices using Sinvoice API
   */
  async getInvoices(
    apiToken: string,
    searchParams: GetInvoicesSearchParams,
  ): Promise<any> {
    // Get customer by API token
    const customer = await this.customerService.getCustomerByApiToken(apiToken);
    if (!customer) {
      throw new UnauthorizedException('Invalid API token');
    }

    try {
      // Get decrypted Sinvoice credentials
      const sinvoiceUsername = customer.sinvoiceUsername;
      const sinvoicePassword = customer.getSinvoicePassword();

      // Create Basic Auth header
      const authHeader = Buffer.from(
        `${sinvoiceUsername}:${sinvoicePassword}`,
      ).toString('base64');

      // Make request to Sinvoice API
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.sinvoiceApiUrl}/services/einvoiceapplication/api/InvoiceAPI/InvoiceUtilsWS/getInvoices/${sinvoiceUsername}`,
          searchParams,
          {
            headers: {
              Authorization: `Basic ${authHeader}`,
              'Content-Type': 'application/json',
              'User-Agent': 'Invoice-Service/1.0',
            },
            timeout: 30000, // 30 seconds timeout
          },
        ),
      );

      // Log successful response (excluding sensitive data)
      this.logger.log(
        `Invoices retrieved successfully for customer: ${customer.user.username}`,
        {
          statusCode: response.status,
          statusText: response.statusText,
          requestParams: {
            startDate: searchParams.startDate,
            endDate: searchParams.endDate,
            pageNum: searchParams.pageNum,
            rowPerPage: searchParams.rowPerPage,
          },
        },
      );

      // Transform and sort the response
      const transformedResponse = this.transformInvoiceResponse(response.data);
      return transformedResponse;
    } catch (error) {
      // Enhanced error logging
      if (error instanceof AxiosError) {
        this.logger.error(
          `Failed to retrieve invoices for customer: ${customer.user.username}`,
          {
            error: {
              message: error.message,
              code: error.code,
              status: error.response?.status,
              statusText: error.response?.statusText,
              data: error.response?.data,
            },
            request: {
              method: error.config?.method?.toUpperCase(),
              url: error.config?.url,
              params: searchParams,
            },
          },
        );

        // Handle Sinvoice specific errors
        return this.handleSinvoiceError(error);
      }

      // Log unexpected errors
      this.logger.error(
        `Unexpected error retrieving invoices for customer: ${customer.user.username}`,
        {
          error: error instanceof Error ? error.stack : error,
        },
      );

      throw new InternalServerErrorException({
        statusCode: 500,
        message: this.errorMessages.DEFAULT,
        error: 'Internal Server Error',
      });
    }
  }

  /**
   * Transform the Sinvoice API response to match the expected format
   * and sort invoices by latest first
   */
  private transformInvoiceResponse(sinvoiceResponse: any): any {
    // Sort invoices by createTime (latest first) or issueDate as fallback
    const sortedInvoices = (sinvoiceResponse.invoices || []).sort(
      (a: any, b: any) => {
        const aTime = a.createTime || a.issueDate || 0;
        const bTime = b.createTime || b.issueDate || 0;
        return bTime - aTime; // Descending order (latest first)
      },
    );

    // Transform the response to match the expected format
    return {
      errorCode: sinvoiceResponse.errorCode || null,
      description: sinvoiceResponse.description || null,
      totalRow: sinvoiceResponse.totalRows || sinvoiceResponse.totalRow || 0,
      invoices: sortedInvoices,
    };
  }
}
