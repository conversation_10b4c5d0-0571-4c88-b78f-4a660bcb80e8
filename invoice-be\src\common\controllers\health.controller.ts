/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { Public } from '../decorators/public.decorator';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    @InjectDataSource() private dataSource: DataSource,
    private configService: ConfigService,
  ) {}

  @Public()
  @Get()
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  @ApiResponse({ status: 503, description: 'Service is unhealthy' })
  async getHealth() {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: this.configService.get('app.nodeEnv'),
      version: process.env.npm_package_version || '1.0.0',
      authentication: {
        jwtEnabled: !!this.configService.get('jwt.secret'),
        jwtExpiresIn: this.configService.get('jwt.expiresIn'),
      },
      database: {
        status: 'unknown',
        ssl: {
          enabled: this.configService.get('database.ssl') !== false,
          mode: process.env.DB_SSL_MODE || 'disabled',
        },
      },
    };

    try {
      // Test database connection
      await this.dataSource.query('SELECT 1');
      health.database.status = 'connected';
    } catch (error) {
      health.database.status = 'disconnected';
      health.status = 'error';
    }

    return health;
  }

  @Public()
  @Get('ssl')
  @ApiOperation({ summary: 'SSL configuration check' })
  @ApiResponse({ status: 200, description: 'SSL configuration details' })
  async getSSLStatus() {
    const sslConfig = this.configService.get('database.ssl');

    return {
      ssl: {
        enabled: sslConfig !== false,
        rejectUnauthorized: sslConfig?.rejectUnauthorized ?? false,
        mode: process.env.DB_SSL_MODE || 'disabled',
        certificates: {
          ca: !!process.env.DB_SSL_CA_CERT_PATH,
          client: !!process.env.DB_SSL_CLIENT_CERT_PATH,
          key: !!process.env.DB_SSL_CLIENT_KEY_PATH,
        },
        cloudProvider: process.env.DB_CLOUD_PROVIDER || 'none',
      },
      environment: this.configService.get('app.nodeEnv'),
      timestamp: new Date().toISOString(),
    };
  }
}
