import {
  Injectable,
  Logger,
  BadRequestException,
  UnauthorizedException,
  InternalServerErrorException,
} from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import {
  SinvoiceApiLoginRequest,
  SinvoiceApiLoginResponse,
} from '../dto/sinvoice-login.dto';

@Injectable()
export class SinvoiceService {
  private readonly logger = new Logger(SinvoiceService.name);
  private readonly sinvoiceApiUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.sinvoiceApiUrl = this.configService.get<string>(
      'SINVOICE_API_URL',
      process.env.SINVOICE_API_URL,
    );

    if (!this.sinvoiceApiUrl) {
      throw new Error('SINVOICE_API_URL is not configured');
    }

    this.logger.log(`Sinvoice API URL configured: ${this.sinvoiceApiUrl}`);
  }

  /**
   * Authenticate with Sinvoice API using customer credentials
   */
  async login(
    username: string,
    password: string,
  ): Promise<SinvoiceApiLoginResponse> {
    const loginRequest: SinvoiceApiLoginRequest = {
      username,
      password,
    };

    try {
      this.logger.log(`Attempting Sinvoice login for username: ${username}`);

      const response = await firstValueFrom(
        this.httpService.post<SinvoiceApiLoginResponse>(
          `${this.sinvoiceApiUrl}/auth/login`,
          loginRequest,
          {
            timeout: 30000, // 30 seconds timeout
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Invoice-Service/1.0',
            },
          },
        ),
      );

      this.logger.log(`Sinvoice login successful for username: ${username}`);
      return response.data;
    } catch (error) {
      this.logger.error(
        `Sinvoice login failed for username: ${username}`,
        error instanceof AxiosError ? error.message : error,
      );

      if (error instanceof AxiosError) {
        // Handle HTTP response errors
        if (error.response?.status === 401) {
          throw new UnauthorizedException('Invalid Sinvoice credentials');
        }
        if (error.response?.status === 400) {
          throw new BadRequestException('Invalid login request format');
        }
        if (error.response?.status >= 500) {
          throw new InternalServerErrorException('Sinvoice API server error');
        }

        // Handle network and connection errors
        if (error.code === 'ECONNABORTED') {
          throw new InternalServerErrorException('Sinvoice API timeout');
        }
        if (error.code === 'ENOTFOUND') {
          throw new InternalServerErrorException(
            `Sinvoice API DNS resolution failed. Please check the API URL: ${this.sinvoiceApiUrl}`,
          );
        }
        if (error.code === 'ECONNREFUSED') {
          throw new InternalServerErrorException(
            'Sinvoice API connection refused. The service may be down.',
          );
        }
        if (error.code === 'ETIMEDOUT') {
          throw new InternalServerErrorException(
            'Sinvoice API connection timeout. Please try again later.',
          );
        }
      }

      throw new InternalServerErrorException('Sinvoice API connection failed');
    }
  }

  /**
   * Refresh Sinvoice access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<SinvoiceApiLoginResponse> {
    try {
      this.logger.log('Attempting to refresh Sinvoice token');

      const response = await firstValueFrom(
        this.httpService.post<SinvoiceApiLoginResponse>(
          `${this.sinvoiceApiUrl}/auth/refresh`,
          { refresh_token: refreshToken },
          {
            timeout: 30000, // 30 seconds timeout
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Invoice-Service/1.0',
            },
          },
        ),
      );

      this.logger.log('Sinvoice token refresh successful');
      return response.data;
    } catch (error) {
      this.logger.error(
        'Sinvoice token refresh failed',
        error instanceof AxiosError ? error.message : error,
      );

      if (error instanceof AxiosError) {
        // Handle HTTP response errors
        if (error.response?.status === 401) {
          throw new UnauthorizedException('Invalid or expired refresh token');
        }
        if (error.response?.status >= 500) {
          throw new InternalServerErrorException('Sinvoice API server error');
        }

        // Handle network and connection errors
        if (error.code === 'ECONNABORTED') {
          throw new InternalServerErrorException('Sinvoice API timeout');
        }
        if (error.code === 'ENOTFOUND') {
          throw new InternalServerErrorException(
            `Sinvoice API DNS resolution failed. Please check the API URL: ${this.sinvoiceApiUrl}`,
          );
        }
        if (error.code === 'ECONNREFUSED') {
          throw new InternalServerErrorException(
            'Sinvoice API connection refused. The service may be down.',
          );
        }
        if (error.code === 'ETIMEDOUT') {
          throw new InternalServerErrorException(
            'Sinvoice API connection timeout. Please try again later.',
          );
        }
      }

      throw new InternalServerErrorException('Sinvoice token refresh failed');
    }
  }

  /**
   * Validate if Sinvoice access token is still valid
   */
  async validateAccessToken(accessToken: string): Promise<boolean> {
    try {
      await firstValueFrom(
        this.httpService.get(`${this.sinvoiceApiUrl}/auth/validate`, {
          timeout: 10000, // 10 seconds timeout for validation
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'User-Agent': 'Invoice-Service/1.0',
          },
        }),
      );
      return true;
    } catch (error) {
      this.logger.warn('Sinvoice access token validation failed');
      return false;
    }
  }
}
