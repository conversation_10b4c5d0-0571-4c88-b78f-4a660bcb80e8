import { DataSource } from 'typeorm';
import { User, UserRole } from '../entities/user.entity';

export class AdminUserSeeder {
  public async run(dataSource: DataSource): Promise<void> {
    const userRepository = dataSource.getRepository(User);

    // Check if admin user already exists
    const existingAdmin = await userRepository.findOne({
      where: { role: UserRole.ADMIN },
    });

    if (existingAdmin) {
      console.log('Admin user already exists, skipping seeder');
      return;
    }

    // Get admin credentials from environment variables
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

    // Check if user with same username or email exists
    const existingUser = await userRepository.findOne({
      where: [{ username: adminUsername }, { email: adminEmail }],
    });

    if (existingUser) {
      console.log(
        `User with username '${adminUsername}' or email '${adminEmail}' already exists`,
      );
      return;
    }

    // Create admin user
    const adminUser = new User();
    adminUser.username = adminUsername;
    adminUser.email = adminEmail;
    adminUser.password = adminPassword; // Will be hashed by the entity's @BeforeInsert hook
    adminUser.role = UserRole.ADMIN;
    adminUser.isActive = true;

    try {
      await userRepository.save(adminUser);
      console.log(`✅ Admin user created successfully:`);
      console.log(`   Username: ${adminUsername}`);
      console.log(`   Email: ${adminEmail}`);
      console.log(`   Password: ${adminPassword}`);
      console.log(`   Role: ${UserRole.ADMIN}`);
      console.log('');
      console.log(
        '⚠️  IMPORTANT: Change the default admin password after first login!',
      );
    } catch (error) {
      console.error('❌ Error creating admin user:', error);
      throw error;
    }
  }
}
