import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import {
  HttpExceptionFilter,
  AllExceptionsFilter,
} from './common/filters/http-exception.filter';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Security middleware
  app.use(helmet());
  app.use(compression());

  // CORS configuration
  app.enableCors({
    origin: configService.get('app.corsOrigin'),
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Global exception filters
  app.useGlobalFilters(new AllExceptionsFilter(), new HttpExceptionFilter());

  // API prefix
  const apiPrefix = configService.get('app.apiPrefix');
  app.setGlobalPrefix(apiPrefix);

  // Swagger documentation
  if (configService.get('app.nodeEnv') !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('Invoice Service API')
      .setDescription(
        `
Invoice Service Backend API Documentation

## Authentication
The API supports two authentication methods:
1. Bearer Token - For internal system authentication
2. API Token - For external service integration (Sinvoice)

### Bearer Token Authentication
Used for most endpoints. Obtain a token via the /auth/login endpoint.
Add the token to requests using the Authorization header:
\`\`\`
Authorization: Bearer <token>
\`\`\`

### API Token Authentication
Used specifically for Sinvoice integration endpoints.
Add your API token to requests using the X-API-Token header:
\`\`\`
X-API-Token: <your-api-token>
\`\`\`

## Rate Limiting
- Most endpoints are limited to 100 requests per minute
- Authentication endpoints are limited to 5 attempts per minute
- Sinvoice integration endpoints are limited to 5 requests per minute

## Error Handling
The API uses standard HTTP status codes and returns error responses in the following format:
\`\`\`json
{
  "statusCode": 400,
  "message": "Error description",
  "error": "Error type"
}
\`\`\`
      `,
      )
      .setVersion('1.0')
      .addBearerAuth()
      .addApiKey(
        { type: 'apiKey', name: 'X-API-Token', in: 'header' },
        'X-API-Token',
      )
      .addTag('Authentication', 'User authentication and session management')
      .addTag('Customers', 'Customer management and Sinvoice integration')
      .addTag('Invoices', 'Invoice creation and management through Sinvoice')
      .addTag('Health', 'System health monitoring')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup(`${apiPrefix}/docs`, app, document);
  }

  const port = configService.get('app.port');
  await app.listen(port);

  logger.log(
    `🚀 Application is running on: http://localhost:${port}/${apiPrefix}`,
  );
  if (configService.get('app.nodeEnv') !== 'production') {
    logger.log(
      `📚 Swagger documentation: http://localhost:${port}/${apiPrefix}/docs`,
    );
  }
}

bootstrap().catch((error) => {
  Logger.error('❌ Error starting server', error, 'Bootstrap');
  process.exit(1);
});
