import * as React from "react";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Input } from "./input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";

export interface PaginationProps extends React.HTMLAttributes<HTMLDivElement> {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  pageSizeOptions?: number[];
  showPageSize?: boolean;
}

export function Pagination({
  page,
  pageSize,
  totalItems,
  totalPages,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 25, 50, 100],
  showPageSize = true,
  className,
  ...props
}: PaginationProps) {
  const [pageInput, setPageInput] = React.useState((page || 1).toString());

  // Ensure we have valid values
  const safePage = page || 1;
  const safePageSize = pageSize || 10;
  const safeTotalItems = totalItems || 0;
  const safeTotalPages = totalPages || 1;

  const startItem = safeTotalItems > 0 ? (safePage - 1) * safePageSize + 1 : 0;
  const endItem = Math.min(safePage * safePageSize, safeTotalItems);

  // Update page input when page prop changes
  React.useEffect(() => {
    setPageInput((page || 1).toString());
  }, [page]);

  const handlePageInputChange = (value: string) => {
    setPageInput(value);
  };

  const handlePageInputSubmit = () => {
    const newPage = parseInt(pageInput, 10);
    if (!isNaN(newPage) && newPage >= 1 && newPage <= safeTotalPages) {
      onPageChange(newPage);
    } else {
      // Reset to current page if invalid
      setPageInput(safePage.toString());
    }
  };

  const handlePageInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handlePageInputSubmit();
    } else if (e.key === "Escape") {
      setPageInput(safePage.toString());
    }
  };

  return (
    <div
      className={cn(
        "flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between py-2",
        className
      )}
      {...props}
    >
      <div className="text-sm text-muted-foreground">
        {safeTotalItems > 0
          ? `Showing ${startItem} to ${endItem} of ${safeTotalItems} items`
          : "No items to display"}
      </div>
      <div className="flex items-center space-x-2">
        {showPageSize && (
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <Select
              value={safePageSize.toString()}
              onValueChange={(value) => onPageSizeChange(Number(value))}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={safePageSize.toString()} />
              </SelectTrigger>
              <SelectContent side="top">
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => onPageChange(1)}
            disabled={safePage <= 1 || safeTotalItems === 0}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => onPageChange(safePage - 1)}
            disabled={safePage <= 1 || safeTotalItems === 0}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Page</span>
            <Input
              type="number"
              min={1}
              max={safeTotalPages}
              value={pageInput}
              onChange={(e) => handlePageInputChange(e.target.value)}
              onBlur={handlePageInputSubmit}
              onKeyDown={handlePageInputKeyDown}
              className="h-8 w-16 text-center text-sm"
              aria-label={`Go to page, current page ${safePage} of ${safeTotalPages}`}
              disabled={safeTotalItems === 0}
            />
            <span className="text-sm text-muted-foreground">
              of {safeTotalPages}
            </span>
          </div>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => onPageChange(safePage + 1)}
            disabled={safePage >= safeTotalPages || safeTotalItems === 0}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => onPageChange(safeTotalPages)}
            disabled={safePage >= safeTotalPages || safeTotalItems === 0}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
