import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { CustomerList } from "../components/customers/customer-list";
import { useCustomerStore } from "../stores/customer.store";
import { useAuthStore } from "../stores/auth.store";
import { Customer, UserRole } from "../types";
import { toast } from "sonner";

export function CustomersPage() {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { resetApiToken, deleteCustomer } = useCustomerStore();

  // Check if user is admin
  const isAdmin = user?.role === UserRole.ADMIN;

  useEffect(() => {
    if (!isAdmin) {
      toast.error("Access denied. Admin privileges required.");
      navigate("/dashboard");
    }
  }, [isAdmin, navigate]);

  const handleCreateCustomer = () => {
    navigate("/customers/create");
  };

  const handleEditCustomer = (customer: Customer) => {
    navigate(`/customers/${customer.id}/edit`);
  };

  const handleViewCustomer = (customer: Customer) => {
    navigate(`/customers/${customer.id}`);
  };

  const handleResetToken = async (customer: Customer) => {
    try {
      await resetApiToken(customer.id);
      toast.success("API token reset successfully");
    } catch (error: any) {
      toast.error("Failed to reset API token", {
        description: error.message || "Please try again later",
      });
    }
  };

  const handleDeleteCustomer = async (customer: Customer) => {
    try {
      await deleteCustomer(customer.id);
      toast.success("Customer deleted successfully");
    } catch (error: any) {
      toast.error("Failed to delete customer", {
        description: error.message || "Please try again later",
      });
    }
  };

  // Redirect non-admin users
  if (!isAdmin) {
    return null;
  }

  return (
    <CustomerList
      onCreateCustomer={handleCreateCustomer}
      onEditCustomer={handleEditCustomer}
      onViewCustomer={handleViewCustomer}
      onResetToken={handleResetToken}
      onDeleteCustomer={handleDeleteCustomer}
    />
  );
}
