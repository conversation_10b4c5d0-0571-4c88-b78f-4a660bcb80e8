version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: invoice-app-prod
    restart: unless-stopped
    ports:
      - '3000:3000'
    environment:
      NODE_ENV: production
      # Database configuration (use external database in production)
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT:-5432}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_DATABASE: ${DB_DATABASE}
      
      # SSL Configuration (enabled for production)
      DB_SSL_ENABLED: ${DB_SSL_ENABLED:-true}
      DB_SSL_REJECT_UNAUTHORIZED: ${DB_SSL_REJECT_UNAUTHORIZED:-false}
      DB_SSL_CA_CERT_PATH: ${DB_SSL_CA_CERT_PATH}
      DB_SSL_CLIENT_CERT_PATH: ${DB_SSL_CLIENT_CERT_PATH}
      DB_SSL_CLIENT_KEY_PATH: ${DB_SSL_CLIENT_KEY_PATH}
      DB_SSL_MODE: ${DB_SSL_MODE:-require}
      
      # Cloud provider (for automatic SSL configuration)
      DB_CLOUD_PROVIDER: ${DB_CLOUD_PROVIDER}
      
      # Connection pool settings
      DB_POOL_MAX: ${DB_POOL_MAX:-20}
      DB_POOL_MIN: ${DB_POOL_MIN:-5}
      DB_POOL_ACQUIRE: ${DB_POOL_ACQUIRE:-30000}
      DB_POOL_IDLE: ${DB_POOL_IDLE:-10000}
      
      # Application configuration
      PORT: ${PORT:-3000}
      API_PREFIX: ${API_PREFIX:-api/v1}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-24h}
      CORS_ORIGIN: ${CORS_ORIGIN}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      
      # Rate limiting
      RATE_LIMIT_TTL: ${RATE_LIMIT_TTL:-60}
      RATE_LIMIT_LIMIT: ${RATE_LIMIT_LIMIT:-100}
      
      # File upload
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-5242880}
      UPLOAD_DEST: ${UPLOAD_DEST:-./uploads}
    volumes:
      # Mount certificates directory (read-only)
      - ./certs:/app/certs:ro
      # Mount uploads directory
      - ./uploads:/app/uploads
    networks:
      - invoice-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  invoice-network:
    driver: bridge
