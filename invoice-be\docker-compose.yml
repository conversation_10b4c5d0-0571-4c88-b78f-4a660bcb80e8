version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: invoice-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: invoice_db
      POSTGRES_USER: invoice_user
      POSTGRES_PASSWORD: invoice_password
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - invoice-network

  redis:
    image: redis:7-alpine
    container_name: invoice-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - invoice-network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: invoice-app
    restart: unless-stopped
    ports:
      - '3000:3000'
    environment:
      NODE_ENV: development
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: invoice_user
      DB_PASSWORD: invoice_password
      DB_DATABASE: invoice_db
      # SSL Configuration (disabled for local development)
      DB_SSL_ENABLED: false
      DB_SSL_REJECT_UNAUTHORIZED: true
      # Uncomment and configure for SSL testing
      # DB_SSL_CA_CERT_PATH: /app/certs/ca-certificate.crt
      # DB_SSL_MODE: prefer
    depends_on:
      - postgres
      - redis
    volumes:
      - .:/app
      - /app/node_modules
      # Mount certificates directory for SSL support
      - ./certs:/app/certs:ro
    networks:
      - invoice-network

volumes:
  postgres_data:
  redis_data:

networks:
  invoice-network:
    driver: bridge
