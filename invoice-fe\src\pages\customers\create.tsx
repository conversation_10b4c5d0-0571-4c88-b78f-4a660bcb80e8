import { useNavigate } from "react-router-dom";
import { useCustomerStore } from "../../stores/customer.store";
import { CreateCustomerForm } from "../../components/customers/create-customer-form";
import { CreateCustomerRequest } from "../../types";
import { CreateCustomerFormData } from "../../lib/validation";

export default function CreateCustomerPage() {
  const navigate = useNavigate();
  const { createCustomer, isLoading } = useCustomerStore();

  const handleSubmit = async (data: CreateCustomerFormData) => {
    await createCustomer(data);
    navigate("/customers");
  };

  const handleCancel = () => {
    navigate("/customers");
  };

  return (
    <div className="container mx-auto py-6">
      <CreateCustomerForm
        isLoading={isLoading}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
}
