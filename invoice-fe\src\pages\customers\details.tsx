import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import { CustomerDetails } from "../../components/customers/customer-details";
import { useCustomerStore } from "../../stores/customer.store";
import { Customer } from "../../types";
import { Button } from "@/components/ui/button";

export default function CustomerDetailsPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [isLoading, setIsLoading] = useState(false);
  const { fetchCustomerById, resetApiToken } = useCustomerStore();
  const [customer, setCustomer] = useState<Customer | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const data = await fetchCustomerById(id);
        setCustomer(data);
      } catch (error: any) {
        const errorMessage = error.response?.data?.message || error.message;
        toast.error("Failed to load customer", {
          description: errorMessage || "Please try again later.",
        });
        navigate("/customers");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomer();
  }, [id, fetchCustomerById, navigate]);

  const handleEdit = () => {
    navigate(`/customers/${id}/edit`);
  };

  const handleBack = () => {
    navigate("/customers");
  };

  const handleResetToken = async () => {
    if (!id || !customer) return;

    try {
      setIsLoading(true);
      await resetApiToken(id);
      const updatedCustomer = await fetchCustomerById(id);
      setCustomer(updatedCustomer);
      toast.success("API token reset successfully", {
        description:
          "Make sure to update all applications using the old token.",
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message;
      toast.error("Failed to reset API token", {
        description: errorMessage || "Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!customer) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-lg font-medium">Loading customer details...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Button onClick={handleBack}>Back</Button>
      <CustomerDetails
        customer={customer}
        onEdit={handleEdit}
        onResetToken={handleResetToken}
        isLoading={isLoading}
      />
    </div>
  );
}
