import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>le,
  CardFooter,
} from "../ui/card";
import { Separator } from "../ui/separator";
import { Form } from "../ui/form";
import { Button } from "../ui/button";
import { CreateTextField, CreatePasswordField } from "./shared/form-fields";
import {
  CreateCustomerFormData,
  createCustomerSchema,
} from "../../lib/validation";
import { CreateCustomerRequest } from "../../types";

interface CreateCustomerFormProps {
  isLoading?: boolean;
  onSubmit: (data: CreateCustomerFormData) => Promise<void>;
  onCancel: () => void;
}

export function CreateCustomerForm({
  isLoading = false,
  onSubmit,
  onCancel,
}: CreateCustomerFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showSinvoicePassword, setShowSinvoicePassword] = useState(false);

  const form = useForm<CreateCustomerFormData>({
    resolver: zodResolver(createCustomerSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      sinvoiceUsername: "",
      sinvoicePassword: "",
    },
  });

  const handleFormSubmit = async (data: CreateCustomerFormData) => {
    try {
      await onSubmit(data);
      toast.success("Customer created successfully");
      form.reset();
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message;

      if (error.response?.status === 409) {
        toast.error("Duplicate data detected", {
          description: errorMessage || "Username or email already exists.",
        });
      } else if (error.response?.status === 400) {
        toast.error("Validation failed", {
          description: errorMessage || "Please check your input and try again.",
        });
      } else {
        toast.error("Failed to create customer", {
          description: errorMessage || "Please try again later.",
        });
      }
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create New Customer</CardTitle>
        <CardDescription>
          Create a new customer account with Sinvoice integration
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          <CardContent className="space-y-6">
            {/* System Account Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">System Account</h3>
              <CreateTextField
                form={form}
                name="username"
                label="Username"
                isLoading={isLoading}
              />
              <CreatePasswordField
                form={form}
                name="password"
                label="Password"
                showPassword={showPassword}
                onTogglePassword={() => setShowPassword(!showPassword)}
                isLoading={isLoading}
              />
              <CreateTextField
                form={form}
                name="email"
                label="Email"
                type="email"
                isLoading={isLoading}
              />
            </div>

            <Separator />

            {/* Sinvoice Integration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Sinvoice Integration</h3>
              <CreateTextField
                form={form}
                name="sinvoiceUsername"
                label="Sinvoice Username"
                isLoading={isLoading}
              />
              <CreatePasswordField
                form={form}
                name="sinvoicePassword"
                label="Sinvoice Password"
                showPassword={showSinvoicePassword}
                onTogglePassword={() =>
                  setShowSinvoicePassword(!showSinvoicePassword)
                }
                isLoading={isLoading}
              />
            </div>
          </CardContent>

          <CardFooter className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              Create Customer
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
