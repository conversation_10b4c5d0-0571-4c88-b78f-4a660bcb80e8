import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),
  PORT: Joi.number().default(3000),
  API_PREFIX: Joi.string().default('api/v1'),

  // Sinvoice API
  SINVOICE_API_URL: Joi.string().uri().required(),

  // Database
  DB_HOST: Joi.string().required(),
  DB_PORT: Joi.number().default(5432),
  DB_USERNAME: Joi.string().required(),
  DB_PASSWORD: Joi.string().required(),
  DB_DATABASE: Joi.string().required(),

  // Database SSL Configuration
  DB_SSL_ENABLED: Joi.boolean().default(false),
  DB_SSL_REJECT_UNAUTHORIZED: Joi.boolean().default(true),
  DB_SSL_CA_CERT_PATH: Joi.string().optional(),
  DB_SSL_CLIENT_CERT_PATH: Joi.string().optional(),
  DB_SSL_CLIENT_KEY_PATH: Joi.string().optional(),
  DB_SSL_MODE: Joi.string()
    .valid('disable', 'allow', 'prefer', 'require', 'verify-ca', 'verify-full')
    .optional(),

  // Database Connection Pool
  DB_POOL_MAX: Joi.number().min(1).default(10),
  DB_POOL_MIN: Joi.number().min(0).default(2),
  DB_POOL_ACQUIRE: Joi.number().min(1000).default(30000),
  DB_POOL_IDLE: Joi.number().min(1000).default(10000),

  // JWT
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRES_IN: Joi.string().default('24h'),

  // Admin Account
  ADMIN_USERNAME: Joi.string().min(3).max(50).default('admin'),
  ADMIN_EMAIL: Joi.string().email().default('<EMAIL>'),
  ADMIN_PASSWORD: Joi.string().min(6).max(255).default('admin123'),

  // CORS
  CORS_ORIGIN: Joi.string().default('http://localhost:3001'),

  // Logging
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug')
    .default('debug'),

  // Rate Limiting
  RATE_LIMIT_TTL: Joi.number().default(60),
  RATE_LIMIT_LIMIT: Joi.number().default(100),

  // File Upload
  MAX_FILE_SIZE: Joi.number().default(5242880),
  UPLOAD_DEST: Joi.string().default('./uploads'),

  // Encryption
  ENCRYPTION_KEY: Joi.string().min(32).required(),
});
