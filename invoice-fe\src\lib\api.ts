import axios, { AxiosInstance, AxiosError, AxiosResponse } from "axios";
import { toast } from "sonner";
import { ApiError } from "../types";

// Create axios instance with base configuration
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:3000/api/v1",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("auth-token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    const apiError: ApiError = {
      message: "An unexpected error occurred",
      statusCode: error.response?.status || 500,
    };

    if (error.response?.data) {
      const errorData = error.response.data as any;
      apiError.message =
        errorData.message || errorData.error || apiError.message;
      apiError.error = errorData.error;
      apiError.timestamp = errorData.timestamp;
      apiError.path = errorData.path;
    } else if (error.message) {
      apiError.message = error.message;
    }

    // Handle different error types with appropriate notifications
    const status = error.response?.status;

    if (status === 401) {
      // Handle unauthorized errors
      localStorage.removeItem("auth-token");
      localStorage.removeItem("auth-user");
      toast.error("Your session has expired. Please log in again.");
      window.location.href = "/login";
    } else if (status === 403) {
      // Handle forbidden errors
      toast.error("You do not have permission to perform this action.");
    } else if (status === 404) {
      // Handle not found errors
      toast.error("The requested resource was not found.");
    } else if (status === 409) {
      // Handle conflict errors (e.g., duplicate data)
      toast.error(
        apiError.message ||
          "A conflict occurred. Please check your data and try again."
      );
    } else if (status === 429) {
      // Handle rate limiting
      toast.warning("Too many requests. Please wait a moment and try again.");
    } else if (status && status >= 500) {
      // Handle server errors
      toast.error("A server error occurred. Please try again later.");
    } else if (!navigator.onLine) {
      // Handle network connectivity issues
      toast.error(
        "No internet connection. Please check your network and try again."
      );
    } else if (error.code === "ECONNABORTED") {
      // Handle timeout errors
      toast.error("Request timed out. Please try again.");
    }

    return Promise.reject(apiError);
  }
);

export default api;
