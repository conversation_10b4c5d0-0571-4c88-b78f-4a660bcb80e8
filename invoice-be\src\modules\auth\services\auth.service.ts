import {
  Injectable,
  UnauthorizedException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { User } from '../../../database/entities/user.entity';
import { LoginDto, AuthResponseDto, UserInfoDto } from '../dto';
import { JwtPayload } from '../strategies/jwt.strategy';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    const { username, password } = loginDto;

    // Find user by username or email
    const user = await this.userRepository.findOne({
      where: [{ username }, { email: username }],
    });

    if (!user) {
      this.logger.warn(`Login attempt with invalid username: ${username}`);
      throw new UnauthorizedException('Invalid credentials');
    }

    if (!user.isActive) {
      this.logger.warn(`Login attempt for deactivated user: ${username}`);
      throw new UnauthorizedException('Account is deactivated');
    }

    // Validate password
    const isPasswordValid = await user.validatePassword(password);
    if (!isPasswordValid) {
      this.logger.warn(`Login attempt with invalid password for user: ${username}`);
      throw new UnauthorizedException('Invalid credentials');
    }

    // Update last login time
    await this.userRepository.update(user.id, {
      lastLoginAt: new Date(),
    });

    // Generate JWT token
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload);
    const expiresIn = this.getTokenExpirationTime();

    this.logger.log(`User ${username} logged in successfully`);

    return {
      accessToken,
      tokenType: 'Bearer',
      expiresIn,
      user: this.mapToUserInfo(user),
    };
  }

  async validateUserById(userId: string): Promise<User | null> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId, isActive: true },
      });
      return user;
    } catch (error) {
      this.logger.error(`Error validating user by ID: ${userId}`, error.stack);
      return null;
    }
  }

  async findUserByUsername(username: string): Promise<User | null> {
    try {
      const user = await this.userRepository.findOne({
        where: [{ username }, { email: username }],
      });
      return user;
    } catch (error) {
      this.logger.error(`Error finding user by username: ${username}`, error.stack);
      return null;
    }
  }

  async getUserProfile(userId: string): Promise<UserInfoDto> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.mapToUserInfo(user);
  }

  private mapToUserInfo(user: User): UserInfoDto {
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
    };
  }

  private getTokenExpirationTime(): number {
    const expiresIn = this.configService.get<string>('jwt.expiresIn', '24h');
    
    // Convert time string to seconds
    if (expiresIn.endsWith('h')) {
      return parseInt(expiresIn.slice(0, -1)) * 3600;
    } else if (expiresIn.endsWith('m')) {
      return parseInt(expiresIn.slice(0, -1)) * 60;
    } else if (expiresIn.endsWith('s')) {
      return parseInt(expiresIn.slice(0, -1));
    } else if (expiresIn.endsWith('d')) {
      return parseInt(expiresIn.slice(0, -1)) * 86400;
    }
    
    // Default to 24 hours if format is not recognized
    return 86400;
  }
}
