import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useAuthStore } from '../auth.store';
import { AuthService } from '../../services/auth.service';
import { UserRole } from '../../types';

// Mock the auth service
vi.mock('../../services/auth.service', () => ({
  AuthService: {
    login: vi.fn(),
    logout: vi.fn(),
    getProfile: vi.fn(),
    setToken: vi.fn(),
    setUser: vi.fn(),
    clearTokens: vi.fn(),
    getToken: vi.fn(),
    getUser: vi.fn(),
  },
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('AuthStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset store state
    useAuthStore.setState({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
    });
  });

  it('initializes with default state', () => {
    const state = useAuthStore.getState();
    
    expect(state.user).toBeNull();
    expect(state.token).toBeNull();
    expect(state.isAuthenticated).toBe(false);
    expect(state.isLoading).toBe(false);
  });

  it('handles successful login', async () => {
    const mockUser = {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      isActive: true,
      lastLoginAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockResponse = {
      accessToken: 'mock-token',
      tokenType: 'Bearer',
      expiresIn: 3600,
      user: mockUser,
    };

    (AuthService.login as any).mockResolvedValue(mockResponse);

    const { login } = useAuthStore.getState();
    
    await login({ username: 'admin', password: 'admin123' });

    const state = useAuthStore.getState();
    expect(state.user).toEqual(mockUser);
    expect(state.token).toBe('mock-token');
    expect(state.isAuthenticated).toBe(true);
    expect(state.isLoading).toBe(false);
    expect(AuthService.setToken).toHaveBeenCalledWith('mock-token');
    expect(AuthService.setUser).toHaveBeenCalledWith(mockUser);
  });

  it('handles login failure', async () => {
    const mockError = new Error('Invalid credentials');
    (AuthService.login as any).mockRejectedValue(mockError);

    const { login } = useAuthStore.getState();
    
    await expect(login({ username: 'admin', password: 'wrong' })).rejects.toThrow('Invalid credentials');

    const state = useAuthStore.getState();
    expect(state.user).toBeNull();
    expect(state.token).toBeNull();
    expect(state.isAuthenticated).toBe(false);
    expect(state.isLoading).toBe(false);
  });

  it('handles logout', async () => {
    // Set initial authenticated state
    useAuthStore.setState({
      user: { id: '1' } as any,
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
    });

    (AuthService.logout as any).mockResolvedValue(undefined);

    const { logout } = useAuthStore.getState();
    await logout();

    const state = useAuthStore.getState();
    expect(state.user).toBeNull();
    expect(state.token).toBeNull();
    expect(state.isAuthenticated).toBe(false);
    expect(state.isLoading).toBe(false);
    expect(AuthService.logout).toHaveBeenCalled();
  });

  it('clears auth state', () => {
    // Set initial authenticated state
    useAuthStore.setState({
      user: { id: '1' } as any,
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
    });

    const { clearAuth } = useAuthStore.getState();
    clearAuth();

    const state = useAuthStore.getState();
    expect(state.user).toBeNull();
    expect(state.token).toBeNull();
    expect(state.isAuthenticated).toBe(false);
    expect(state.isLoading).toBe(false);
    expect(AuthService.clearTokens).toHaveBeenCalled();
  });

  it('checks auth with valid token and user', async () => {
    const mockUser = {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      isActive: true,
      lastLoginAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    (AuthService.getToken as any).mockReturnValue('mock-token');
    (AuthService.getUser as any).mockReturnValue(mockUser);
    (AuthService.getProfile as any).mockResolvedValue(mockUser);

    const { checkAuth } = useAuthStore.getState();
    await checkAuth();

    const state = useAuthStore.getState();
    expect(state.user).toEqual(mockUser);
    expect(state.token).toBe('mock-token');
    expect(state.isAuthenticated).toBe(true);
    expect(state.isLoading).toBe(false);
  });

  it('clears auth when token is invalid', async () => {
    (AuthService.getToken as any).mockReturnValue('invalid-token');
    (AuthService.getUser as any).mockReturnValue({ id: '1' });
    (AuthService.getProfile as any).mockRejectedValue(new Error('Unauthorized'));

    const { checkAuth } = useAuthStore.getState();
    await checkAuth();

    const state = useAuthStore.getState();
    expect(state.user).toBeNull();
    expect(state.token).toBeNull();
    expect(state.isAuthenticated).toBe(false);
    expect(state.isLoading).toBe(false);
  });
});
