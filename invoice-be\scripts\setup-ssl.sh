#!/bin/bash

# SSL Setup Script for Invoice Service Database Connections
# This script helps set up SSL certificates for different cloud providers

set -e

echo "🔐 Invoice Service - Database SSL Setup"
echo "======================================"

# Create certificates directory if it doesn't exist
mkdir -p certs

# Function to download AWS RDS CA certificate
setup_aws_rds() {
    echo "📥 Downloading AWS RDS CA certificate..."
    curl -o certs/ca-certificate.crt https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem
    echo "✅ AWS RDS CA certificate downloaded to certs/ca-certificate.crt"
    
    echo ""
    echo "🔧 Add these environment variables to your .env file:"
    echo "DB_SSL_ENABLED=true"
    echo "DB_SSL_REJECT_UNAUTHORIZED=false"
    echo "DB_SSL_CA_CERT_PATH=./certs/ca-certificate.crt"
    echo "DB_SSL_MODE=require"
    echo "DB_CLOUD_PROVIDER=aws"
}

# Function to setup Google Cloud SQL
setup_gcp_sql() {
    echo "📋 Google Cloud SQL SSL Setup Instructions:"
    echo ""
    echo "1. Download the server CA certificate from Google Cloud Console:"
    echo "   - Go to your Cloud SQL instance"
    echo "   - Navigate to Connections > Security"
    echo "   - Download the server CA certificate"
    echo "   - Save it as certs/ca-certificate.crt"
    echo ""
    echo "2. Or use gcloud CLI:"
    echo "   gcloud sql ssl-certs describe [CERT_NAME] --instance=[INSTANCE_NAME] --format=\"get(cert)\" > certs/ca-certificate.crt"
    echo ""
    echo "🔧 Add these environment variables to your .env file:"
    echo "DB_SSL_ENABLED=true"
    echo "DB_SSL_REJECT_UNAUTHORIZED=false"
    echo "DB_SSL_CA_CERT_PATH=./certs/ca-certificate.crt"
    echo "DB_SSL_MODE=require"
    echo "DB_CLOUD_PROVIDER=gcp"
}

# Function to setup Azure Database
setup_azure_db() {
    echo "📋 Azure Database SSL Setup Instructions:"
    echo ""
    echo "1. Download the SSL certificate from Azure portal:"
    echo "   - Go to your Azure Database for PostgreSQL"
    echo "   - Navigate to Connection security"
    echo "   - Download the SSL certificate"
    echo "   - Save it as certs/ca-certificate.crt"
    echo ""
    echo "2. Or download directly:"
    echo "   curl -o certs/ca-certificate.crt https://www.digicert.com/CACerts/BaltimoreCyberTrustRoot.crt.pem"
    echo ""
    echo "🔧 Add these environment variables to your .env file:"
    echo "DB_SSL_ENABLED=true"
    echo "DB_SSL_REJECT_UNAUTHORIZED=false"
    echo "DB_SSL_CA_CERT_PATH=./certs/ca-certificate.crt"
    echo "DB_SSL_MODE=require"
    echo "DB_CLOUD_PROVIDER=azure"
}

# Function to setup custom SSL
setup_custom_ssl() {
    echo "📋 Custom SSL Setup Instructions:"
    echo ""
    echo "1. Obtain your SSL certificates from your database provider"
    echo "2. Place the certificates in the certs/ directory:"
    echo "   - CA certificate: certs/ca-certificate.crt"
    echo "   - Client certificate (if required): certs/client-certificate.crt"
    echo "   - Client private key (if required): certs/client-key.key"
    echo ""
    echo "3. Set proper file permissions:"
    echo "   chmod 644 certs/ca-certificate.crt"
    echo "   chmod 644 certs/client-certificate.crt"
    echo "   chmod 600 certs/client-key.key"
    echo ""
    echo "🔧 Add these environment variables to your .env file:"
    echo "DB_SSL_ENABLED=true"
    echo "DB_SSL_REJECT_UNAUTHORIZED=true"
    echo "DB_SSL_CA_CERT_PATH=./certs/ca-certificate.crt"
    echo "DB_SSL_CLIENT_CERT_PATH=./certs/client-certificate.crt"
    echo "DB_SSL_CLIENT_KEY_PATH=./certs/client-key.key"
    echo "DB_SSL_MODE=verify-full"
    echo "DB_CLOUD_PROVIDER=custom"
}

# Function to disable SSL
disable_ssl() {
    echo "🔧 To disable SSL, add this environment variable to your .env file:"
    echo "DB_SSL_ENABLED=false"
    echo ""
    echo "⚠️  Note: SSL should be enabled in production environments!"
}

# Main menu
echo ""
echo "Please select your database provider:"
echo "1) AWS RDS"
echo "2) Google Cloud SQL"
echo "3) Azure Database"
echo "4) Custom SSL setup"
echo "5) Disable SSL (development only)"
echo ""
read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        setup_aws_rds
        ;;
    2)
        setup_gcp_sql
        ;;
    3)
        setup_azure_db
        ;;
    4)
        setup_custom_ssl
        ;;
    5)
        disable_ssl
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "✅ SSL setup complete!"
echo ""
echo "📝 Next steps:"
echo "1. Update your .env file with the provided environment variables"
echo "2. Restart your application to apply the SSL configuration"
echo "3. Check the application logs to verify SSL connection"
echo ""
echo "🔍 For troubleshooting, enable debug logging:"
echo "LOG_LEVEL=debug"
