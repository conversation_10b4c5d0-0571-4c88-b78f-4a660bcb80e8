import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { CalendarIcon, Search, RefreshCw, Filter, X } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

import { InvoiceFilterProps, InvoiceFilterFormData, FILTER_VALIDATION } from "../../types";

// Validation schema based on backend requirements
const invoiceFilterSchema = z.object({
  startDate: z.string().min(1, "Start date is required").max(50, "Start date must not exceed 50 characters"),
  endDate: z.string().min(1, "End date is required").max(50, "End date must not exceed 50 characters"),
  invoiceNo: z.string().optional().refine((val) => {
    if (!val || val.trim() === '') return true;
    return val.length >= 7 && val.length <= 35 && FILTER_VALIDATION.invoiceNo.pattern.test(val);
  }, "Invoice number must be 7-35 characters and contain only alphanumeric characters"),
  invoiceType: z.string().optional(),
  buyerTaxCode: z.string().optional().refine((val) => {
    if (!val || val.trim() === '') return true;
    return val.length <= 20;
  }, "Buyer tax code must not exceed 20 characters"),
  buyerIdNo: z.string().optional(),
  templateCode: z.string().optional(),
  invoiceSeri: z.string().optional().refine((val) => {
    if (!val || val.trim() === '') return true;
    return val.length <= 25 && FILTER_VALIDATION.invoiceSeri.pattern.test(val);
  }, "Invoice series must not exceed 25 characters and contain only alphanumeric characters"),
  issueStartDate: z.string().optional(),
  issueEndDate: z.string().optional(),
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: "Start date must be before or equal to end date",
  path: ["endDate"],
}).refine((data) => {
  if (data.issueStartDate && data.issueEndDate) {
    return new Date(data.issueStartDate) <= new Date(data.issueEndDate);
  }
  return true;
}, {
  message: "Issue start date must be before or equal to issue end date",
  path: ["issueEndDate"],
});

export function InvoiceFilters({ filters, onFiltersChange, onSearch, isLoading }: InvoiceFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  const form = useForm<InvoiceFilterFormData>({
    resolver: zodResolver(invoiceFilterSchema),
    defaultValues: {
      startDate: filters.startDate || '',
      endDate: filters.endDate || '',
      invoiceNo: filters.invoiceNo || '',
      invoiceType: filters.invoiceType || '',
      buyerTaxCode: filters.buyerTaxCode || '',
      buyerIdNo: filters.buyerIdNo || '',
      templateCode: filters.templateCode || '',
      invoiceSeri: filters.invoiceSeri || '',
      issueStartDate: filters.issueStartDate || '',
      issueEndDate: filters.issueEndDate || '',
    },
  });

  // Update form when filters change
  useEffect(() => {
    form.reset({
      startDate: filters.startDate || '',
      endDate: filters.endDate || '',
      invoiceNo: filters.invoiceNo || '',
      invoiceType: filters.invoiceType || '',
      buyerTaxCode: filters.buyerTaxCode || '',
      buyerIdNo: filters.buyerIdNo || '',
      templateCode: filters.templateCode || '',
      invoiceSeri: filters.invoiceSeri || '',
      issueStartDate: filters.issueStartDate || '',
      issueEndDate: filters.issueEndDate || '',
    });
  }, [filters, form]);

  // Count active filters
  useEffect(() => {
    const values = form.getValues();
    const count = Object.values(values).filter(value => value && value.trim() !== '').length;
    setActiveFiltersCount(count);
  }, [form.watch()]);

  const handleSubmit = (data: InvoiceFilterFormData) => {
    // Clean up empty values
    const cleanedData = Object.entries(data).reduce((acc, [key, value]) => {
      if (value && value.trim() !== '') {
        acc[key as keyof InvoiceFilterFormData] = value.trim();
      }
      return acc;
    }, {} as Partial<InvoiceFilterFormData>);

    onFiltersChange(cleanedData);
    onSearch();
  };

  const handleReset = () => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const defaultValues = {
      startDate: thirtyDaysAgo.toISOString().split('T')[0],
      endDate: today.toISOString().split('T')[0],
      invoiceNo: '',
      invoiceType: '',
      buyerTaxCode: '',
      buyerIdNo: '',
      templateCode: '',
      invoiceSeri: '',
      issueStartDate: '',
      issueEndDate: '',
    };

    form.reset(defaultValues);
    onFiltersChange(defaultValues);
  };

  const handleQuickDateRange = (days: number) => {
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - days);

    const values = {
      startDate: startDate.toISOString().split('T')[0],
      endDate: today.toISOString().split('T')[0],
    };

    form.setValue('startDate', values.startDate);
    form.setValue('endDate', values.endDate);
    onFiltersChange(values);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Invoice Filters
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFiltersCount} active
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Filter invoices by date range and other criteria
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Less Filters' : 'More Filters'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Required Filters - Always Visible */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date *</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date *</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="invoiceNo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Invoice Number</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter invoice number"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="buyerTaxCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Buyer Tax Code</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter tax code"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Quick Date Range Buttons */}
            <div className="flex flex-wrap gap-2">
              <Label className="text-sm font-medium">Quick ranges:</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleQuickDateRange(7)}
                disabled={isLoading}
              >
                Last 7 days
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleQuickDateRange(30)}
                disabled={isLoading}
              >
                Last 30 days
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleQuickDateRange(90)}
                disabled={isLoading}
              >
                Last 90 days
              </Button>
            </div>

            {/* Optional Filters - Expandable */}
            {isExpanded && (
              <>
                <Separator />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="invoiceType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Type</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter invoice type"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="buyerIdNo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Buyer ID Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter buyer ID"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="templateCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Template Code</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter template code"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="invoiceSeri"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Series</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter invoice series"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="issueStartDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Issue Start Date</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="issueEndDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Issue End Date</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </>
            )}

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-2 pt-4">
              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
                Search Invoices
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Reset Filters
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
