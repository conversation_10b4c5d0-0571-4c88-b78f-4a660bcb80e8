# SSL Certificates for Database Connections

This directory contains SSL certificates for secure database connections.

## Certificate Types

### CA Certificate (`ca-certificate.crt`)
- Certificate Authority certificate
- Used to verify the database server's identity
- Required for cloud database providers (AWS RDS, Google Cloud SQL, etc.)

### Client Certificate (`client-certificate.crt`)
- Client certificate for mutual TLS authentication
- Optional - only needed if your database requires client certificates
- Must be paired with a client private key

### Client Private Key (`client-key.key`)
- Private key corresponding to the client certificate
- **CRITICAL**: Never commit this file to version control
- Set file permissions to 600 (read/write for owner only)

## Cloud Provider Certificates

### AWS RDS
Download the RDS CA certificate:
```bash
curl -o certs/ca-certificate.crt https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem
```

### Google Cloud SQL
Download from the Google Cloud Console or use gcloud CLI:
```bash
gcloud sql ssl-certs describe [CERT_NAME] --instance=[INSTANCE_NAME] --format="get(cert)" > certs/ca-certificate.crt
```

### Azure Database
Download from the Azure portal or use Azure CLI:
```bash
az postgres server show --resource-group [RESOURCE_GROUP] --name [SERVER_NAME] --query "sslEnforcement"
```

## Environment Configuration

Set these environment variables to use the certificates:

```bash
# Enable SSL
DB_SSL_ENABLED=true

# Certificate paths
DB_SSL_CA_CERT_PATH=./certs/ca-certificate.crt
DB_SSL_CLIENT_CERT_PATH=./certs/client-certificate.crt  # Optional
DB_SSL_CLIENT_KEY_PATH=./certs/client-key.key          # Optional

# SSL validation (set to false for cloud providers)
DB_SSL_REJECT_UNAUTHORIZED=false

# SSL mode
DB_SSL_MODE=require
```

## Security Best Practices

1. **File Permissions**: Set restrictive permissions on certificate files
   ```bash
   chmod 600 certs/client-key.key
   chmod 644 certs/ca-certificate.crt
   chmod 644 certs/client-certificate.crt
   ```

2. **Version Control**: Add certificate files to `.gitignore`
   ```
   certs/*.crt
   certs/*.key
   certs/*.pem
   ```

3. **Production**: Use secure certificate management systems like:
   - AWS Secrets Manager
   - Azure Key Vault
   - Google Secret Manager
   - HashiCorp Vault

4. **Certificate Rotation**: Regularly update certificates before expiration

## Troubleshooting

### Common SSL Connection Issues

1. **Certificate not found**: Check file paths and permissions
2. **SSL handshake failed**: Verify certificate validity and compatibility
3. **Connection timeout**: Check network connectivity and firewall rules
4. **Certificate verification failed**: Ensure CA certificate matches the database server

### Debug SSL Configuration

Enable debug logging to troubleshoot SSL issues:
```bash
LOG_LEVEL=debug
```

The application will log SSL configuration details during startup.
