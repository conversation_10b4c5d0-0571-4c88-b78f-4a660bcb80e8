import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { CustomerService } from '../services/customer.service';
import {
  CreateCustomerDto,
  CustomerResponseDto,
  UpdateCustomerDto,
  ApiTokenResponseDto,
  ResetApiTokenDto,
  SinvoiceLoginDto,
  SinvoiceLoginResponseDto,
} from '../dto';
import { Roles } from '../../../common/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators/current-user.decorator';
import { UserRole, User } from '../../../database/entities/user.entity';
import { HTTP_STATUS_MESSAGES } from '../../../common/constants/app.constants';

@ApiTags('Customers')
@ApiBearerAuth()
@Controller('customers')
export class CustomerController {
  constructor(private readonly customerService: CustomerService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @Roles(UserRole.ADMIN)
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 attempts per minute
  @ApiOperation({ summary: 'Create a new customer account' })
  @ApiBody({ type: CreateCustomerDto })
  @ApiResponse({
    status: 201,
    description: 'Customer created successfully',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - username or email already exists',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin role required',
  })
  async createCustomer(
    @Body() createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    return this.customerService.createCustomer(createCustomerDto);
  }

  @Get()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all customers (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Customers retrieved successfully',
    type: [CustomerResponseDto],
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin role required',
  })
  async getAllCustomers(): Promise<CustomerResponseDto[]> {
    return this.customerService.getAllCustomers();
  }

  @Get('me')
  @Roles(UserRole.CUSTOMER)
  @ApiOperation({ summary: 'Get current customer profile' })
  @ApiResponse({
    status: 200,
    description: 'Customer profile retrieved successfully',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - customer role required',
  })
  async getCurrentCustomer(
    @CurrentUser() user: User,
  ): Promise<CustomerResponseDto> {
    return this.customerService.getCustomerByUserId(user.id);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get customer by ID (admin only)' })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Customer retrieved successfully',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin role required',
  })
  async getCustomerById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CustomerResponseDto> {
    return this.customerService.getCustomerById(id);
  }

  @Put('me')
  @Roles(UserRole.CUSTOMER)
  @ApiOperation({ summary: 'Update current customer profile' })
  @ApiBody({ type: UpdateCustomerDto })
  @ApiResponse({
    status: 200,
    description: 'Customer updated successfully',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 404,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - email already exists',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - customer role required',
  })
  async updateCurrentCustomer(
    @CurrentUser() user: User,
    @Body() updateCustomerDto: UpdateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const customer = await this.customerService.getCustomerByUserId(user.id);
    return this.customerService.updateCustomer(customer.id, updateCustomerDto);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update customer by ID (admin only)' })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({ type: UpdateCustomerDto })
  @ApiResponse({
    status: 200,
    description: 'Customer updated successfully',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 404,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - email already exists',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin role required',
  })
  async updateCustomer(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
  ): Promise<CustomerResponseDto> {
    return this.customerService.updateCustomer(id, updateCustomerDto);
  }

  @Post(':id/reset-token')
  @HttpCode(HttpStatus.OK)
  @Roles(UserRole.ADMIN)
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 attempts per minute
  @ApiOperation({ summary: 'Reset customer API token (admin only)' })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({ type: ResetApiTokenDto })
  @ApiResponse({
    status: 200,
    description: 'API token reset successfully',
    type: ApiTokenResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid confirmation',
  })
  @ApiResponse({
    status: 404,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin role required',
  })
  async resetApiToken(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() resetApiTokenDto: ResetApiTokenDto,
  ): Promise<ApiTokenResponseDto> {
    // Validate confirmation
    if (resetApiTokenDto.confirmation !== 'RESET_TOKEN') {
      throw new BadRequestException(
        'Invalid confirmation. Please provide "RESET_TOKEN" as confirmation.',
      );
    }

    return this.customerService.resetApiToken(id);
  }

  @Post('sinvoice/login')
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 attempts per minute
  @ApiOperation({
    summary: 'Login to Sinvoice using customer API token',
    description:
      'Authenticate with Sinvoice API using customer credentials and return access tokens',
  })
  @ApiBody({ type: SinvoiceLoginDto })
  @ApiResponse({
    status: 200,
    description: 'Sinvoice login successful',
    type: SinvoiceLoginResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid API token format',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid API token or Sinvoice credentials',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error - Sinvoice API connection failed',
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests - rate limit exceeded',
  })
  async sinvoiceLogin(
    @Body() sinvoiceLoginDto: SinvoiceLoginDto,
  ): Promise<SinvoiceLoginResponseDto> {
    return this.customerService.sinvoiceLogin(sinvoiceLoginDto.apiToken);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete customer by ID (admin only)' })
  @ApiParam({
    name: 'id',
    description: 'Customer ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Customer deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin role required',
  })
  async deleteCustomer(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.customerService.deleteCustomer(id);
  }
}
