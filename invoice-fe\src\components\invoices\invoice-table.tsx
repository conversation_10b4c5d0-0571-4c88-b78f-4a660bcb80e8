import { useState } from "react";
import { <PERSON><PERSON>pDown, ArrowU<PERSON>, ArrowDown, FileText, Download, Eye } from "lucide-react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { InvoiceTableProps, Invoice, InvoiceTableColumn } from "../../types";
import { ColumnSelector } from "./column-selector";

export function InvoiceTable({
  invoices,
  columns,
  columnVisibility,
  onColumnVisibilityChange,
  sortBy,
  sortOrder,
  onSort,
  isLoading,
}: InvoiceTableProps) {
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);

  // Filter visible columns
  const visibleColumns = columns.filter(column => columnVisibility[column.key]);

  const handleSort = (columnKey: string) => {
    if (!columns.find(col => col.key === columnKey)?.sortable) return;
    
    if (sortBy === columnKey) {
      // Toggle sort order
      const newOrder = sortOrder === 'asc' ? 'desc' : 'asc';
      onSort(columnKey);
    } else {
      // New column, start with ascending
      onSort(columnKey);
    }
  };

  const getSortIcon = (columnKey: string) => {
    if (sortBy !== columnKey) {
      return <ArrowUpDown className="h-4 w-4 text-muted-foreground" />;
    }
    return sortOrder === 'asc' 
      ? <ArrowUp className="h-4 w-4 text-primary" />
      : <ArrowDown className="h-4 w-4 text-primary" />;
  };

  const formatCellValue = (value: any, column: InvoiceTableColumn): string => {
    if (value === null || value === undefined) return '-';
    
    if (column.format) {
      return column.format(value);
    }
    
    // Default formatting based on column type
    switch (column.key) {
      case 'issueDate':
      case 'createdDate':
      case 'modifiedDate':
        return new Date(value).toLocaleDateString('vi-VN');
      case 'total':
        return new Intl.NumberFormat('vi-VN', { 
          style: 'currency', 
          currency: 'VND' 
        }).format(value);
      default:
        return String(value);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'SIGNED': { variant: 'default' as const, label: 'Signed' },
      'PENDING': { variant: 'secondary' as const, label: 'Pending' },
      'CANCELLED': { variant: 'destructive' as const, label: 'Cancelled' },
      'DRAFT': { variant: 'outline' as const, label: 'Draft' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || 
                  { variant: 'outline' as const, label: status };

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const handleInvoiceAction = (invoice: Invoice, action: 'view' | 'download') => {
    // These would typically navigate to invoice details or trigger download
    console.log(`${action} invoice:`, invoice.invoiceId);
    // TODO: Implement actual actions
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-24" />
        </div>
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                {visibleColumns.map((column) => (
                  <TableHead key={column.key}>
                    <Skeleton className="h-4 w-full" />
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  {visibleColumns.map((column) => (
                    <TableCell key={column.key}>
                      <Skeleton className="h-4 w-full" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (invoices.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Invoices</h3>
          <ColumnSelector
            columns={columns}
            columnVisibility={columnVisibility}
            onColumnVisibilityChange={onColumnVisibilityChange}
          />
        </div>
        <div className="border rounded-lg p-8 text-center">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No invoices found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search filters or date range.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Table Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">
          Invoices ({invoices.length})
        </h3>
        <ColumnSelector
          columns={columns}
          columnVisibility={columnVisibility}
          onColumnVisibilityChange={onColumnVisibilityChange}
        />
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              {visibleColumns.map((column) => (
                <TableHead
                  key={column.key}
                  className={`${column.width ? `w-[${column.width}]` : ''} ${
                    column.align === 'right' ? 'text-right' : 
                    column.align === 'center' ? 'text-center' : 'text-left'
                  }`}
                >
                  {column.sortable ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 font-medium hover:bg-transparent"
                      onClick={() => handleSort(column.key)}
                    >
                      <span className="flex items-center gap-2">
                        {column.label}
                        {getSortIcon(column.key)}
                      </span>
                    </Button>
                  ) : (
                    column.label
                  )}
                </TableHead>
              ))}
              <TableHead className="w-[100px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {invoices.map((invoice) => (
              <TableRow
                key={invoice.invoiceId}
                className="hover:bg-muted/50 cursor-pointer"
                onClick={() => handleInvoiceAction(invoice, 'view')}
              >
                {visibleColumns.map((column) => (
                  <TableCell
                    key={column.key}
                    className={`${
                      column.align === 'right' ? 'text-right' : 
                      column.align === 'center' ? 'text-center' : 'text-left'
                    }`}
                  >
                    {column.key === 'status' ? (
                      getStatusBadge(invoice[column.key] as string)
                    ) : column.key === 'invoiceNo' ? (
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {formatCellValue(invoice[column.key], column)}
                      </code>
                    ) : (
                      formatCellValue(invoice[column.key], column)
                    )}
                  </TableCell>
                ))}
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <span className="sr-only">Open menu</span>
                        <div className="h-4 w-4 flex items-center justify-center">
                          ⋮
                        </div>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleInvoiceAction(invoice, 'view');
                        }}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleInvoiceAction(invoice, 'download');
                        }}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download PDF
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Table Footer with Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div>
          Showing {invoices.length} invoice{invoices.length !== 1 ? 's' : ''}
        </div>
        <div className="flex items-center gap-4">
          {selectedInvoices.length > 0 && (
            <span>{selectedInvoices.length} selected</span>
          )}
          <span>
            Total: {new Intl.NumberFormat('vi-VN', { 
              style: 'currency', 
              currency: 'VND' 
            }).format(
              invoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0)
            )}
          </span>
        </div>
      </div>
    </div>
  );
}
