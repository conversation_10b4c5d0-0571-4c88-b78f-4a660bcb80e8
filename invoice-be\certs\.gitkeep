# SSL Certificates Directory
# 
# This directory is for storing SSL certificates for database connections.
# 
# Structure:
# - ca-certificate.crt     # Certificate Authority certificate
# - client-certificate.crt # Client certificate (for mutual TLS)
# - client-key.key        # Client private key (for mutual TLS)
#
# Security Notes:
# - Never commit actual certificate files to version control
# - Use environment variables to specify certificate paths
# - Ensure proper file permissions (600 for private keys)
# - For production, use secure certificate management systems
