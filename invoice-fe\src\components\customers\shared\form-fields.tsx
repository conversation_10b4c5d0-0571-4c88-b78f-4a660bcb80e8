import { <PERSON>, EyeOff } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { Button } from "../../ui/button";
import { Input } from "../../ui/input";
import { Checkbox } from "../../ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../ui/form";
import {
  CreateCustomerFormData,
  UpdateCustomerFormData,
} from "../../../lib/validation";

interface BaseFieldProps {
  label: string;
  isLoading?: boolean;
}

// Create form fields
interface CreatePasswordFieldProps extends BaseFieldProps {
  form: UseFormReturn<CreateCustomerFormData>;
  name: "password" | "sinvoicePassword";
  showPassword: boolean;
  onTogglePassword: () => void;
}

export function CreatePasswordField({
  form,
  name,
  label,
  showPassword,
  onTogglePassword,
  isLoading,
}: CreatePasswordFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                placeholder={`Enter ${label.toLowerCase()}`}
                disabled={isLoading}
                {...field}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={onTogglePassword}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

interface CreateTextFieldProps extends BaseFieldProps {
  form: UseFormReturn<CreateCustomerFormData>;
  name: "username" | "email" | "sinvoiceUsername";
  type?: string;
  placeholder?: string;
}

export function CreateTextField({
  form,
  name,
  label,
  type = "text",
  placeholder,
  isLoading,
}: CreateTextFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              type={type}
              placeholder={placeholder || `Enter ${label.toLowerCase()}`}
              disabled={isLoading}
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Edit form fields
interface EditPasswordFieldProps extends BaseFieldProps {
  form: UseFormReturn<UpdateCustomerFormData>;
  name: "password" | "sinvoicePassword";
  showPassword: boolean;
  onTogglePassword: () => void;
}

export function EditPasswordField({
  form,
  name,
  label,
  showPassword,
  onTogglePassword,
  isLoading,
}: EditPasswordFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                placeholder={`Enter ${label.toLowerCase()}`}
                disabled={isLoading}
                {...field}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={onTogglePassword}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

interface EditTextFieldProps extends BaseFieldProps {
  form: UseFormReturn<UpdateCustomerFormData>;
  name: "username" | "email" | "sinvoiceUsername";
  type?: string;
  placeholder?: string;
}

export function EditTextField({
  form,
  name,
  label,
  type = "text",
  placeholder,
  isLoading,
}: EditTextFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              type={type}
              placeholder={placeholder || `Enter ${label.toLowerCase()}`}
              disabled={isLoading}
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

interface EditCheckboxFieldProps extends BaseFieldProps {
  form: UseFormReturn<UpdateCustomerFormData>;
  name: "isActive";
}

export function EditCheckboxField({
  form,
  name,
  label,
  isLoading,
}: EditCheckboxFieldProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4">
          <FormControl>
            <Checkbox
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={isLoading}
            />
          </FormControl>
          <FormLabel className="font-normal">{label}</FormLabel>
        </FormItem>
      )}
    />
  );
}
