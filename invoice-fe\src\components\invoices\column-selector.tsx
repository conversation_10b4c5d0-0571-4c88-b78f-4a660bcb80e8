import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Eye, EyeOff, RotateCcw } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuHeader,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

import { InvoiceTableColumn, ColumnVisibility, DEFAULT_COLUMN_VISIBILITY } from "../../types";

interface ColumnSelectorProps {
  columns: InvoiceTableColumn[];
  columnVisibility: ColumnVisibility;
  onColumnVisibilityChange: (visibility: ColumnVisibility) => void;
}

export function ColumnSelector({ 
  columns, 
  columnVisibility, 
  onColumnVisibilityChange 
}: ColumnSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Count visible columns
  const visibleCount = Object.values(columnVisibility).filter(Boolean).length;
  const totalCount = columns.length;

  const handleColumnToggle = (columnKey: string, checked: boolean) => {
    const newVisibility = {
      ...columnVisibility,
      [columnKey]: checked,
    };
    onColumnVisibilityChange(newVisibility);
  };

  const handleSelectAll = () => {
    const allVisible = columns.reduce((acc, column) => {
      acc[column.key] = true;
      return acc;
    }, {} as ColumnVisibility);
    onColumnVisibilityChange(allVisible);
  };

  const handleSelectNone = () => {
    const allHidden = columns.reduce((acc, column) => {
      acc[column.key] = false;
      return acc;
    }, {} as ColumnVisibility);
    onColumnVisibilityChange(allHidden);
  };

  const handleResetToDefault = () => {
    onColumnVisibilityChange(DEFAULT_COLUMN_VISIBILITY);
  };

  const getColumnStatus = (columnKey: string): 'visible' | 'hidden' => {
    return columnVisibility[columnKey] ? 'visible' : 'hidden';
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          Columns
          <Badge variant="secondary" className="ml-1">
            {visibleCount}/{totalCount}
          </Badge>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Column Visibility</span>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetToDefault}
              className="h-6 px-2 text-xs"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset
            </Button>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        {/* Quick Actions */}
        <div className="px-2 py-1">
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSelectAll}
              className="h-6 px-2 text-xs flex-1"
            >
              Show All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSelectNone}
              className="h-6 px-2 text-xs flex-1"
            >
              Hide All
            </Button>
          </div>
        </div>
        
        <DropdownMenuSeparator />
        
        {/* Column List */}
        <div className="max-h-64 overflow-y-auto">
          {columns.map((column) => {
            const isVisible = columnVisibility[column.key];
            const status = getColumnStatus(column.key);
            
            return (
              <DropdownMenuItem
                key={column.key}
                className="flex items-center gap-3 px-3 py-2 cursor-pointer"
                onSelect={(e) => e.preventDefault()}
                onClick={() => handleColumnToggle(column.key, !isVisible)}
              >
                <Checkbox
                  checked={isVisible}
                  onCheckedChange={(checked) => 
                    handleColumnToggle(column.key, checked as boolean)
                  }
                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
                
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  {status === 'visible' ? (
                    <Eye className="h-3 w-3 text-green-600 flex-shrink-0" />
                  ) : (
                    <EyeOff className="h-3 w-3 text-gray-400 flex-shrink-0" />
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <Label 
                      htmlFor={`column-${column.key}`}
                      className={`text-sm cursor-pointer truncate block ${
                        isVisible ? 'text-foreground' : 'text-muted-foreground'
                      }`}
                    >
                      {column.label}
                    </Label>
                    {column.width && (
                      <div className="text-xs text-muted-foreground">
                        {column.width}
                      </div>
                    )}
                  </div>
                </div>
              </DropdownMenuItem>
            );
          })}
        </div>
        
        <DropdownMenuSeparator />
        
        {/* Summary */}
        <div className="px-3 py-2 text-xs text-muted-foreground">
          {visibleCount} of {totalCount} columns visible
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Preset configurations for common use cases
export const COLUMN_PRESETS = {
  minimal: {
    invoiceNo: true,
    issueDate: true,
    buyerName: true,
    total: true,
    status: true,
    invoiceType: false,
    templateCode: false,
    buyerTaxCode: false,
  },
  detailed: {
    invoiceNo: true,
    issueDate: true,
    buyerTaxCode: true,
    buyerName: true,
    total: true,
    status: true,
    invoiceType: true,
    templateCode: true,
  },
  financial: {
    invoiceNo: true,
    issueDate: true,
    buyerName: true,
    total: true,
    status: true,
    invoiceType: false,
    templateCode: false,
    buyerTaxCode: false,
  },
} as const;

// Enhanced column selector with presets
interface EnhancedColumnSelectorProps extends ColumnSelectorProps {
  showPresets?: boolean;
}

export function EnhancedColumnSelector({ 
  columns, 
  columnVisibility, 
  onColumnVisibilityChange,
  showPresets = false
}: EnhancedColumnSelectorProps) {
  const handlePresetSelect = (presetName: keyof typeof COLUMN_PRESETS) => {
    const preset = COLUMN_PRESETS[presetName];
    onColumnVisibilityChange(preset);
  };

  if (!showPresets) {
    return (
      <ColumnSelector
        columns={columns}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={onColumnVisibilityChange}
      />
    );
  }

  return (
    <div className="flex items-center gap-2">
      <ColumnSelector
        columns={columns}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={onColumnVisibilityChange}
      />
      
      {/* Preset Buttons */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePresetSelect('minimal')}
          className="text-xs"
        >
          Minimal
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePresetSelect('detailed')}
          className="text-xs"
        >
          Detailed
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePresetSelect('financial')}
          className="text-xs"
        >
          Financial
        </Button>
      </div>
    </div>
  );
}
