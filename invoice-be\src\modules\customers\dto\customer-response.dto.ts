import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../../../database/entities/user.entity';

export class CustomerUserDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Username',
    example: 'customer123',
  })
  username: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    example: UserRole.CUSTOMER,
  })
  role: UserRole;

  @ApiProperty({
    description: 'Account status',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Last login timestamp',
    example: '2023-12-01T10:00:00.000Z',
    nullable: true,
  })
  lastLoginAt: Date | null;

  @ApiProperty({
    description: 'Account creation timestamp',
    example: '2023-12-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Account last update timestamp',
    example: '2023-12-01T10:00:00.000Z',
  })
  updatedAt: Date;
}

export class CustomerResponseDto {
  @ApiProperty({
    description: 'Customer ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'User information',
    type: CustomerUserDto,
  })
  user: CustomerUserDto;

  @ApiProperty({
    description: 'Sinvoice username',
    example: 'sinvoice_user',
  })
  sinvoiceUsername: string;

  @ApiProperty({
    description: 'API token for accessing services',
    example: 'abc123def456...',
  })
  apiToken: string;

  @ApiProperty({
    description: 'Whether Sinvoice tokens are active',
    example: false,
  })
  hasSinvoiceTokens: boolean;

  @ApiProperty({
    description: 'Sinvoice token expiration timestamp',
    example: '2023-12-01T10:00:00.000Z',
    nullable: true,
  })
  sinvoiceTokenExpiresAt: Date | null;

  @ApiProperty({
    description: 'Customer creation timestamp',
    example: '2023-12-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Customer last update timestamp',
    example: '2023-12-01T10:00:00.000Z',
  })
  updatedAt: Date;
}
