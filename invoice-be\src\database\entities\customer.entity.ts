import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import * as crypto from 'crypto';
import { User } from './user.entity';

@Entity('customers')
export class Customer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  @OneToOne(() => User, { eager: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'varchar', length: 255 })
  @Exclude({ toPlainOnly: true })
  sinvoiceUsername: string;

  @Column({ type: 'text' })
  @Exclude({ toPlainOnly: true })
  sinvoicePasswordEncrypted: string;

  @Column({ type: 'varchar', length: 32 })
  @Exclude({ toPlainOnly: true })
  sinvoicePasswordIv: string;

  @Column({ type: 'varchar', length: 32, nullable: true })
  @Exclude({ toPlainOnly: true })
  sinvoicePasswordAuthTag: string | null;

  @Column({ type: 'varchar', length: 64, unique: true })
  apiToken: string;

  @Column({ type: 'text', nullable: true })
  @Exclude({ toPlainOnly: true })
  sinvoiceAccessToken: string | null;

  @Column({ type: 'text', nullable: true })
  @Exclude({ toPlainOnly: true })
  sinvoiceRefreshToken: string | null;

  @Column({ type: 'timestamp', nullable: true })
  sinvoiceTokenExpiresAt: Date | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @BeforeInsert()
  generateApiToken() {
    if (!this.apiToken) {
      this.apiToken = this.generateSecureToken();
    }
  }

  /**
   * Generate a cryptographically secure API token
   */
  private generateSecureToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Regenerate API token (for admin reset functionality)
   */
  regenerateApiToken(): void {
    this.apiToken = this.generateSecureToken();
  }

  /**
   * Encrypt Sinvoice password using AES-256-GCM
   */
  static encryptSinvoicePassword(password: string): {
    encrypted: string;
    iv: string;
    authTag: string;
  } {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(
      process.env.ENCRYPTION_KEY ||
        'default-encryption-key-change-in-production',
      'salt',
      32,
    );
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(password, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Get the authentication tag for GCM mode
    const authTag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
    };
  }

  /**
   * Decrypt Sinvoice password with backward compatibility
   */
  static decryptSinvoicePassword(
    encrypted: string,
    iv: string,
    authTag?: string | null,
  ): string {
    const key = crypto.scryptSync(
      process.env.ENCRYPTION_KEY ||
        'default-encryption-key-change-in-production',
      'salt',
      32,
    );
    const ivBuffer = Buffer.from(iv, 'hex');

    try {
      if (authTag) {
        // New format with authentication tag (AES-256-GCM)
        const algorithm = 'aes-256-gcm';
        const decipher = crypto.createDecipheriv(algorithm, key, ivBuffer);
        decipher.setAuthTag(Buffer.from(authTag, 'hex'));

        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        return decrypted;
      } else {
        // Legacy format without authentication tag
        // The original implementation was incorrectly using GCM without auth tag
        // We need to try both GCM (for recently created data) and CBC (fallback)
        try {
          // First try: AES-256-GCM without auth tag (for data created with the buggy implementation)
          const gcmAlgorithm = 'aes-256-gcm';
          const gcmDecipher = crypto.createDecipheriv(
            gcmAlgorithm,
            key,
            ivBuffer,
          );

          let decrypted = gcmDecipher.update(encrypted, 'hex', 'utf8');
          decrypted += gcmDecipher.final('utf8');

          return decrypted;
        } catch (gcmError) {
          // Fallback: Try AES-256-CBC for older legacy data
          try {
            const cbcAlgorithm = 'aes-256-cbc';
            const cbcDecipher = crypto.createDecipheriv(
              cbcAlgorithm,
              key,
              ivBuffer,
            );

            let decrypted = cbcDecipher.update(encrypted, 'hex', 'utf8');
            decrypted += cbcDecipher.final('utf8');

            return decrypted;
          } catch (cbcError) {
            throw new Error(
              `Failed to decrypt legacy password with both GCM and CBC: GCM error: ${gcmError instanceof Error ? gcmError.message : 'Unknown'}, CBC error: ${cbcError instanceof Error ? cbcError.message : 'Unknown'}`,
            );
          }
        }
      }
    } catch (error) {
      throw new Error(
        `Failed to decrypt Sinvoice password: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get decrypted Sinvoice password
   */
  getSinvoicePassword(): string {
    return Customer.decryptSinvoicePassword(
      this.sinvoicePasswordEncrypted,
      this.sinvoicePasswordIv,
      this.sinvoicePasswordAuthTag,
    );
  }

  /**
   * Set Sinvoice password (encrypts automatically)
   */
  setSinvoicePassword(password: string): void {
    const { encrypted, iv, authTag } =
      Customer.encryptSinvoicePassword(password);
    this.sinvoicePasswordEncrypted = encrypted;
    this.sinvoicePasswordIv = iv;
    this.sinvoicePasswordAuthTag = authTag;
  }

  /**
   * Migrate legacy encrypted password to new format with authentication tag
   * This method should be called for customers with null authTag
   */
  migrateLegacyPassword(): void {
    if (this.sinvoicePasswordAuthTag) {
      // Already migrated
      return;
    }

    try {
      // Decrypt using legacy method (without auth tag)
      const decryptedPassword = Customer.decryptSinvoicePassword(
        this.sinvoicePasswordEncrypted,
        this.sinvoicePasswordIv,
        null,
      );

      // Re-encrypt with new format (with auth tag)
      this.setSinvoicePassword(decryptedPassword);
    } catch (error) {
      throw new Error(
        `Failed to migrate legacy password: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Update Sinvoice session tokens
   */
  updateSinvoiceTokens(
    accessToken: string,
    refreshToken: string,
    expiresIn: number,
  ): void {
    this.sinvoiceAccessToken = accessToken;
    this.sinvoiceRefreshToken = refreshToken;
    this.sinvoiceTokenExpiresAt = new Date(Date.now() + expiresIn * 1000);
  }

  /**
   * Check if Sinvoice token is expired
   */
  isSinvoiceTokenExpired(): boolean {
    if (!this.sinvoiceTokenExpiresAt) {
      return true;
    }
    return new Date() >= this.sinvoiceTokenExpiresAt;
  }

  /**
   * Clear Sinvoice session tokens
   */
  clearSinvoiceTokens(): void {
    this.sinvoiceAccessToken = null;
    this.sinvoiceRefreshToken = null;
    this.sinvoiceTokenExpiresAt = null;
  }

  toJSON() {
    const {
      sinvoicePasswordEncrypted,
      sinvoicePasswordIv,
      sinvoiceAccessToken,
      sinvoiceRefreshToken,
      ...result
    } = this;
    return result;
  }
}
