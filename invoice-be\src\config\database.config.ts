/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Creates SSL configuration object based on environment variables
 */
function createSSLConfig() {
  const sslEnabled = process.env.DB_SSL_ENABLED === 'true';

  if (!sslEnabled) {
    return false;
  }

  const sslConfig: any = {
    rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
  };

  // Add SSL certificate files if provided
  if (process.env.DB_SSL_CA_CERT_PATH) {
    const caPath = path.resolve(process.env.DB_SSL_CA_CERT_PATH);
    if (fs.existsSync(caPath)) {
      sslConfig.ca = fs.readFileSync(caPath);
    }
  }

  if (process.env.DB_SSL_CLIENT_CERT_PATH) {
    const certPath = path.resolve(process.env.DB_SSL_CLIENT_CERT_PATH);
    if (fs.existsSync(certPath)) {
      sslConfig.cert = fs.readFileSync(certPath);
    }
  }

  if (process.env.DB_SSL_CLIENT_KEY_PATH) {
    const keyPath = path.resolve(process.env.DB_SSL_CLIENT_KEY_PATH);
    if (fs.existsSync(keyPath)) {
      sslConfig.key = fs.readFileSync(keyPath);
    }
  }

  // Handle SSL mode
  if (process.env.DB_SSL_MODE) {
    sslConfig.sslmode = process.env.DB_SSL_MODE;
  }

  return sslConfig;
}

export default registerAs(
  'database',
  (): TypeOrmModuleOptions => ({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'invoice_user',
    password: process.env.DB_PASSWORD || 'invoice_password',
    database: process.env.DB_DATABASE || 'invoice_db',
    entities: [__dirname + '/../database/entities/*.entity{.ts,.js}'],
    migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
    synchronize: process.env.NODE_ENV === 'development',
    logging: process.env.NODE_ENV === 'development',
    ssl: createSSLConfig(),
    // Connection pool settings for better performance with SSL
    extra: {
      max: parseInt(process.env.DB_POOL_MAX, 10) || 10,
      min: parseInt(process.env.DB_POOL_MIN, 10) || 2,
      acquire: parseInt(process.env.DB_POOL_ACQUIRE, 10) || 30000,
      idle: parseInt(process.env.DB_POOL_IDLE, 10) || 10000,
    },
  }),
);
