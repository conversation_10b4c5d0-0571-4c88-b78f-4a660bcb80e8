import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { AppModule } from '../src/app.module';
import { CreateCustomerDto } from '../src/modules/customers/dto';

describe('CustomerController (e2e)', () => {
  let app: INestApplication<App>;
  let adminToken: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Login as admin to get token for testing
    const loginResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({
        username: 'admin',
        password: 'admin123',
      })
      .expect(200);

    adminToken = loginResponse.body.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/api/v1/customers (POST)', () => {
    it('should create a new customer', async () => {
      const createCustomerDto: CreateCustomerDto = {
        username: 'testcustomer',
        email: '<EMAIL>',
        password: 'password123',
        sinvoiceUsername: 'sinvoice_test',
        sinvoicePassword: 'sinvoice_pass123',
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/customers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createCustomerDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('apiToken');
      expect(response.body.user.username).toBe(createCustomerDto.username);
      expect(response.body.user.email).toBe(createCustomerDto.email);
      expect(response.body.sinvoiceUsername).toBe(createCustomerDto.sinvoiceUsername);
      expect(response.body).not.toHaveProperty('sinvoicePasswordEncrypted');
    });

    it('should fail to create customer without admin role', async () => {
      const createCustomerDto: CreateCustomerDto = {
        username: 'testcustomer2',
        email: '<EMAIL>',
        password: 'password123',
        sinvoiceUsername: 'sinvoice_test2',
        sinvoicePassword: 'sinvoice_pass123',
      };

      await request(app.getHttpServer())
        .post('/api/v1/customers')
        .send(createCustomerDto)
        .expect(401); // No token provided
    });
  });

  describe('/api/v1/customers (GET)', () => {
    it('should get all customers for admin', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/customers')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });
});
