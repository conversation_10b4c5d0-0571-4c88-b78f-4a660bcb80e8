// Invoice data interfaces based on backend API response
export interface Invoice {
  invoiceId: string;
  invoiceNo: string;
  issueDate: string;
  buyerTaxCode: string;
  buyerName?: string;
  buyerIdNo?: string;
  total: number;
  status: string;
  invoiceType?: string;
  templateCode?: string;
  invoiceSeries?: string;
  currencyCode?: string;
  paymentStatus?: boolean;
  createdDate?: string;
  modifiedDate?: string;
}

// Filter interfaces based on GetInvoicesDto
export interface InvoiceFilters {
  // Required filters
  apiToken: string;
  startDate: string;
  endDate: string;
  rowPerPage: number;
  pageNum: number;

  // Optional filters
  invoiceNo?: string;
  invoiceType?: string;
  buyerTaxCode?: string;
  buyerIdNo?: string;
  templateCode?: string;
  invoiceSeri?: string;
  getAll?: boolean;
  issueStartDate?: string;
  issueEndDate?: string;
}

// API request/response interfaces
export interface GetInvoicesRequest {
  apiToken: string;
  startDate: string;
  endDate: string;
  rowPerPage: number;
  pageNum: number;
  invoiceNo?: string;
  invoiceType?: string;
  buyerTaxCode?: string;
  buyerIdNo?: string;
  templateCode?: string;
  invoiceSeri?: string;
  getAll?: boolean;
  issueStartDate?: string;
  issueEndDate?: string;
}

export interface GetInvoicesResponse {
  status: string;
  message: string;
  data: {
    totalRecords: number;
    pageNum: number;
    rowPerPage: number;
    invoices: Invoice[];
  };
}

// Column configuration for table
export interface InvoiceTableColumn {
  key: keyof Invoice;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  format?: (value: any) => string;
}

// Column visibility settings
export interface ColumnVisibility {
  [key: string]: boolean;
}

// Pagination interface
export interface InvoicePagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// Store state interface
export interface InvoiceState {
  invoices: Invoice[];
  isLoading: boolean;
  error: string | null;
  filters: Partial<InvoiceFilters>;
  pagination: InvoicePagination;
  columnVisibility: ColumnVisibility;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Store actions interface
export interface InvoiceActions {
  // Data fetching
  fetchInvoices: (filters: GetInvoicesRequest) => Promise<void>;
  
  // State management
  setFilters: (filters: Partial<InvoiceFilters>) => void;
  setPagination: (pagination: Partial<InvoicePagination>) => void;
  setColumnVisibility: (visibility: ColumnVisibility) => void;
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  clearError: () => void;
  reset: () => void;
}

// Combined store interface
export interface InvoiceStore extends InvoiceState, InvoiceActions {}

// Component prop interfaces
export interface InvoiceFilterProps {
  filters: Partial<InvoiceFilters>;
  onFiltersChange: (filters: Partial<InvoiceFilters>) => void;
  onSearch: () => void;
  isLoading?: boolean;
}

export interface InvoiceTableProps {
  invoices: Invoice[];
  columns: InvoiceTableColumn[];
  columnVisibility: ColumnVisibility;
  onColumnVisibilityChange: (visibility: ColumnVisibility) => void;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSort: (column: string) => void;
  isLoading?: boolean;
}

export interface InvoicePaginationProps {
  pagination: InvoicePagination;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  isLoading?: boolean;
}

// Form validation interfaces
export interface InvoiceFilterFormData {
  startDate: string;
  endDate: string;
  invoiceNo?: string;
  invoiceType?: string;
  buyerTaxCode?: string;
  buyerIdNo?: string;
  templateCode?: string;
  invoiceSeri?: string;
  issueStartDate?: string;
  issueEndDate?: string;
}

// Default column configuration
export const DEFAULT_INVOICE_COLUMNS: InvoiceTableColumn[] = [
  { key: 'invoiceNo', label: 'Invoice No', sortable: true, width: '150px' },
  { key: 'issueDate', label: 'Issue Date', sortable: true, width: '120px', format: (date) => new Date(date).toLocaleDateString() },
  { key: 'buyerTaxCode', label: 'Buyer Tax Code', sortable: true, width: '130px' },
  { key: 'buyerName', label: 'Buyer Name', sortable: true, width: '200px' },
  { key: 'total', label: 'Total', sortable: true, width: '120px', align: 'right', format: (value) => new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value) },
  { key: 'status', label: 'Status', sortable: true, width: '100px' },
  { key: 'invoiceType', label: 'Type', sortable: true, width: '100px' },
  { key: 'templateCode', label: 'Template', sortable: false, width: '120px' },
];

// Default column visibility
export const DEFAULT_COLUMN_VISIBILITY: ColumnVisibility = {
  invoiceNo: true,
  issueDate: true,
  buyerTaxCode: true,
  buyerName: true,
  total: true,
  status: true,
  invoiceType: false,
  templateCode: false,
};

// Filter validation rules
export const FILTER_VALIDATION = {
  invoiceNo: {
    minLength: 7,
    maxLength: 35,
    pattern: /^[a-zA-Z0-9]*$/,
  },
  invoiceSeri: {
    maxLength: 25,
    pattern: /^[a-zA-Z0-9]*$/,
  },
  buyerTaxCode: {
    maxLength: 20,
  },
  startDate: {
    maxLength: 50,
  },
  endDate: {
    maxLength: 50,
  },
  issueStartDate: {
    maxLength: 50,
  },
  issueEndDate: {
    maxLength: 50,
  },
};
