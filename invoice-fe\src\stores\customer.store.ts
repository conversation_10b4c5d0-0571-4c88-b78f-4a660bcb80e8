import { create } from "zustand";
import { toast } from "sonner";
import { CustomerService } from "../services/customer.service";
import {
  CustomerStore,
  Customer,
  CreateCustomerRequest,
  UpdateCustomerRequest,
  ApiTokenResponse,
  CustomerFilters,
} from "../types";

export const useCustomerStore = create<CustomerStore>((set, get) => ({
  // Initial state
  customers: [],
  selectedCustomer: null,
  isLoading: false,
  error: null,
  filters: {},
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },

  // Data fetching
  fetchCustomers: async (params) => {
    set({ isLoading: true, error: null });
    try {
      const { filters, pagination } = get();
      const searchParams = {
        page: params?.page || pagination.page,
        limit: params?.limit || pagination.limit,
        search: params?.search || filters.search,
        isActive: filters.isActive,
        hasSinvoiceTokens: filters.hasSinvoiceTokens,
      };

      const customers = await CustomerService.searchCustomers(searchParams);

      set({
        customers,
        isLoading: false,
        pagination: {
          ...pagination,
          page: searchParams.page,
          limit: searchParams.limit,
          total: customers.length, // Note: Backend should return total count
          totalPages: Math.ceil(customers.length / searchParams.limit),
        },
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || "Failed to fetch customers",
      });
      toast.error(error.message || "Failed to fetch customers");
    }
  },

  fetchCustomerById: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const customer = await CustomerService.getCustomerById(id);
      set({ selectedCustomer: customer, isLoading: false });
      return customer;
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || "Failed to fetch customer",
      });
      toast.error(error.message || "Failed to fetch customer");
      throw error;
    }
  },

  // CRUD operations
  createCustomer: async (data: CreateCustomerRequest) => {
    set({ isLoading: true, error: null });
    try {
      const customer = await CustomerService.createCustomer(data);

      // Add to customers list
      set((state) => ({
        customers: [customer, ...state.customers],
        isLoading: false,
      }));

      toast.success("Customer created successfully!");
      return customer;
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || "Failed to create customer",
      });
      toast.error(error.message || "Failed to create customer");
      throw error;
    }
  },

  updateCustomer: async (id: string, data: UpdateCustomerRequest) => {
    set({ isLoading: true, error: null });
    try {
      const updatedCustomer = await CustomerService.updateCustomer(id, data);

      // Update in customers list
      set((state) => ({
        customers: state.customers.map((customer) =>
          customer.id === id ? updatedCustomer : customer
        ),
        selectedCustomer:
          state.selectedCustomer?.id === id
            ? updatedCustomer
            : state.selectedCustomer,
        isLoading: false,
      }));

      toast.success("Customer updated successfully!");
      return updatedCustomer;
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || "Failed to update customer",
      });
      toast.error(error.message || "Failed to update customer");
      throw error;
    }
  },

  resetApiToken: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const result = await CustomerService.resetApiToken(id, {
        confirmation: "RESET_TOKEN",
      });

      // Refresh customer data to get new token
      await get().fetchCustomerById(id);

      set({ isLoading: false });
      toast.success(result.message);
      return result;
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || "Failed to reset API token",
      });
      toast.error(error.message || "Failed to reset API token");
      throw error;
    }
  },

  deleteCustomer: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await CustomerService.deleteCustomer(id);

      // Remove from customers list
      set((state) => ({
        customers: state.customers.filter((customer) => customer.id !== id),
        selectedCustomer:
          state.selectedCustomer?.id === id ? null : state.selectedCustomer,
        isLoading: false,
      }));

      toast.success("Customer deleted successfully!");
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || "Failed to delete customer",
      });
      toast.error(error.message || "Failed to delete customer");
      throw error;
    }
  },

  // State management
  setSelectedCustomer: (customer: Customer | null) => {
    set({ selectedCustomer: customer });
  },

  setFilters: (filters: Partial<CustomerFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
    }));
    // Automatically refetch with new filters
    get().fetchCustomers();
  },

  clearError: () => {
    set({ error: null });
  },

  // Pagination
  setPage: (page: number) => {
    set((state) => ({
      pagination: { ...state.pagination, page },
    }));
    get().fetchCustomers({ page });
  },

  setLimit: (limit: number) => {
    set((state) => ({
      pagination: { ...state.pagination, limit, page: 1 },
    }));
    get().fetchCustomers({ limit, page: 1 });
  },
}));
