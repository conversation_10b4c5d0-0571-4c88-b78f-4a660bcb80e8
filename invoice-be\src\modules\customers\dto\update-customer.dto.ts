import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
  IsEmail,
  IsNotEmpty,
  IsBoolean,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCustomerDto {
  @ApiProperty({
    description: 'Customer username',
    example: 'newusername',
    minLength: 3,
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username?: string;

  @ApiProperty({
    description: 'Customer email address',
    example: '<EMAIL>',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiProperty({
    description: 'Customer password',
    example: 'newpassword123',
    minLength: 6,
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(6)
  @MaxLength(255)
  password?: string;

  @ApiProperty({
    description: 'Customer account status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Sinvoice username for API integration',
    example: 'new_sinvoice_user',
    minLength: 3,
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  sinvoiceUsername?: string;

  @ApiProperty({
    description: 'Sinvoice password for API integration',
    example: 'new_sinvoice_password',
    minLength: 6,
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(6)
  @MaxLength(255)
  sinvoicePassword?: string;
}

export class ResetApiTokenDto {
  @ApiProperty({
    description: 'Confirmation message',
    example: 'RESET_TOKEN',
  })
  @IsString()
  @IsNotEmpty()
  confirmation: string;
}

export class ApiTokenResponseDto {
  @ApiProperty({
    description: 'New API token',
    example: 'abc123def456...',
  })
  apiToken: string;

  @ApiProperty({
    description: 'Success message',
    example: 'API token reset successfully',
  })
  message: string;
}
