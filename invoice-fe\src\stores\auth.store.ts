import { create } from "zustand";
import { AuthStore, LoginRequest, User } from "../types";
import { AuthService } from "../services/auth.service";
import { toast } from "sonner";

export const useAuthStore = create<AuthStore>((set, get) => ({
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,

  login: async (credentials: LoginRequest) => {
    set({ isLoading: true });
    try {
      const response = await AuthService.login(credentials);

      // Store token and user data
      AuthService.setToken(response.accessToken);
      AuthService.setUser(response.user);

      set({
        user: response.user,
        token: response.accessToken,
        isAuthenticated: true,
        isLoading: false,
      });

      toast.success("Login successful!");
    } catch (error: any) {
      set({ isLoading: false });
      toast.error(error.message || "Login failed");
      throw error;
    }
  },

  logout: async () => {
    set({ isLoading: true });
    try {
      await AuthService.logout();
      get().clearAuth();
      toast.success("Logged out successfully");
    } catch (error: any) {
      // Clear auth even if logout API fails
      get().clearAuth();
      toast.error("Logout failed, but you have been logged out locally");
    }
  },

  setUser: (user: User) => {
    AuthService.setUser(user);
    set({ user, isAuthenticated: true });
  },

  setToken: (token: string) => {
    AuthService.setToken(token);
    set({ token, isAuthenticated: true });
  },

  clearAuth: () => {
    AuthService.clearTokens();
    set({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
    });
  },

  checkAuth: async () => {
    const token = AuthService.getToken();
    const user = AuthService.getUser();

    if (!token || !user) {
      get().clearAuth();
      return;
    }

    set({ isLoading: true });
    try {
      // Verify token is still valid by fetching profile
      const currentUser = await AuthService.getProfile();
      set({
        user: currentUser,
        token,
        isAuthenticated: true,
        isLoading: false,
      });
    } catch (error: any) {
      // Token is invalid, clear auth
      get().clearAuth();

      // Show session expiration notification if it was a 401 error
      if (error.statusCode === 401) {
        toast.warning("Your session has expired. Please log in again.");
      } else {
        toast.error("Authentication check failed. Please log in again.");
      }
    }
  },

  // New method for handling session expiration warnings
  showSessionExpirationWarning: () => {
    toast.warning(
      "Your session will expire soon. Please save your work and refresh the page to continue.",
      {
        duration: 10000, // Show for 10 seconds
        action: {
          label: "Refresh",
          onClick: () => window.location.reload(),
        },
      }
    );
  },

  // New method for handling token refresh
  refreshToken: async () => {
    try {
      // In a real app, this would call a refresh token endpoint
      // For now, we'll just check if the current token is still valid
      await AuthService.getProfile();
      toast.success("Session refreshed successfully");
    } catch (error: any) {
      get().clearAuth();
      toast.error("Failed to refresh session. Please log in again.");
      throw error;
    }
  },
}));
