import { render, screen, fireEvent } from '@testing-library/react';
import { Pagination } from './pagination';

describe('Pagination Component', () => {
  const defaultProps = {
    page: 1,
    pageSize: 10,
    totalItems: 100,
    totalPages: 10,
    onPageChange: jest.fn(),
    onPageSizeChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing with valid props', () => {
    render(<Pagination {...defaultProps} />);
    expect(screen.getByText('Showing 1 to 10 of 100 items')).toBeInTheDocument();
  });

  it('handles undefined/null props gracefully', () => {
    const propsWithUndefined = {
      page: undefined as any,
      pageSize: undefined as any,
      totalItems: undefined as any,
      totalPages: undefined as any,
      onPageChange: jest.fn(),
      onPageSizeChange: jest.fn(),
    };

    render(<Pagination {...propsWithUndefined} />);
    expect(screen.getByText('No items to display')).toBeInTheDocument();
  });

  it('allows direct page input', () => {
    render(<Pagination {...defaultProps} />);
    
    const pageInput = screen.getByRole('spinbutton');
    fireEvent.change(pageInput, { target: { value: '5' } });
    fireEvent.blur(pageInput);
    
    expect(defaultProps.onPageChange).toHaveBeenCalledWith(5);
  });

  it('validates page input range', () => {
    render(<Pagination {...defaultProps} />);
    
    const pageInput = screen.getByRole('spinbutton');
    
    // Test invalid high value
    fireEvent.change(pageInput, { target: { value: '15' } });
    fireEvent.blur(pageInput);
    
    // Should not call onPageChange for invalid value
    expect(defaultProps.onPageChange).not.toHaveBeenCalledWith(15);
  });

  it('handles page size changes', () => {
    render(<Pagination {...defaultProps} />);
    
    // This would require more complex testing with the Select component
    // For now, we verify the component renders the page size selector
    expect(screen.getByText('Rows per page')).toBeInTheDocument();
  });

  it('disables navigation when no items', () => {
    const emptyProps = {
      ...defaultProps,
      totalItems: 0,
      totalPages: 0,
    };

    render(<Pagination {...emptyProps} />);
    
    const buttons = screen.getAllByRole('button');
    const navigationButtons = buttons.filter(btn => 
      btn.getAttribute('aria-label')?.includes('page') || 
      btn.querySelector('svg')
    );
    
    navigationButtons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });
});
