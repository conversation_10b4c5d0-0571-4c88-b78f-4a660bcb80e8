import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSinvoicePasswordAuthTag1749019096821
  implements MigrationInterface
{
  name = 'AddSinvoicePasswordAuthTag1749019096821';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add authentication tag column for AES-256-GCM encryption
    await queryRunner.addColumn(
      'customers',
      new TableColumn({
        name: 'sinvoicePasswordAuthTag',
        type: 'varchar',
        length: '32',
        isNullable: true, // Allow null for backward compatibility
      }),
    );

    // Create index for better performance
    await queryRunner.query(
      `CREATE INDEX "IDX_customers_sinvoicePasswordAuthTag" ON "customers" ("sinvoicePasswordAuthTag")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_customers_sinvoicePasswordAuthTag"`,
    );

    // Drop column
    await queryRunner.dropColumn('customers', 'sinvoicePasswordAuthTag');
  }
}
