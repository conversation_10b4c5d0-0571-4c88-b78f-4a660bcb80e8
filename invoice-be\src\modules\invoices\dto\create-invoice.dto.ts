import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsNumber,
  IsArray,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

export class GeneralInvoiceInfoDto {
  @ApiProperty({ example: '01GTKT' })
  @IsString()
  invoiceType: string;

  @ApiProperty({ example: '01GTKT0/002' })
  @IsString()
  templateCode: string;

  @ApiProperty({ example: 'C25TVA' })
  @IsString()
  invoiceSeries: string;

  @ApiProperty({ example: 'VND' })
  @IsString()
  currencyCode: string;

  @ApiProperty({ example: '1' })
  @IsString()
  adjustmentType: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  paymentStatus: boolean;

  @ApiProperty({ example: true })
  @IsBoolean()
  cusGetInvoiceRight: boolean;
}

export class SellerInfoDto {
  @ApiProperty({ example: 'Meo Cung Shop' })
  @IsString()
  sellerLegalName: string;

  @ApiProperty({ example: '**********-507' })
  @IsString()
  sellerTaxCode: string;

  @ApiProperty({ example: '123 Đường ABC, Quận XYZ, Thành Phố ABC, Việt Nam' })
  @IsString()
  sellerAddressLine: string;

  @ApiProperty({ example: '**********' })
  @IsString()
  sellerPhoneNumber: string;

  @ApiProperty({ example: '**********' })
  @IsString()
  sellerFaxNumber: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  sellerEmail: string;

  @ApiProperty({ example: 'Ngân hàng Vietcombank' })
  @IsString()
  sellerBankName: string;

  @ApiProperty({ example: '**********01' })
  @IsString()
  sellerBankAccount: string;

  @ApiProperty({ example: '' })
  @IsString()
  sellerDistrictName: string;

  @ApiProperty({ example: 'Thành Phố Hà Nội' })
  @IsString()
  sellerCityName: string;

  @ApiProperty({ example: '84' })
  @IsString()
  sellerCountryCode: string;

  @ApiProperty({ example: 'sinvoice.viettel.vn' })
  @IsString()
  sellerWebsite: string;
}

export class BuyerInfoDto {
  @ApiProperty({ example: 'Tên khách hàng' })
  @IsString()
  buyerName: string;

  @ApiProperty({ example: 'Tên đơn vị' })
  @IsString()
  buyerLegalName: string;

  @ApiProperty({ example: '**********' })
  @IsString()
  buyerTaxCode: string;

  @ApiProperty({ example: 'An Khánh Hoài Đức Hà Nội' })
  @IsString()
  buyerAddressLine: string;

  @ApiProperty({ example: '**********' })
  @IsString()
  buyerPostalCode: string;

  @ApiProperty({
    example: 'Số 9, đường 11, VSIP Bắc Ninh, Thị xã Từ Sơn, Tỉnh',
  })
  @IsString()
  buyerDistrictName: string;

  @ApiProperty({ example: 'Thành Phố Hà Nội' })
  @IsString()
  buyerCityName: string;

  @ApiProperty({ example: '84' })
  @IsString()
  buyerCountryCode: string;

  @ApiProperty({ example: '*********' })
  @IsString()
  buyerPhoneNumber: string;

  @ApiProperty({ example: '0458954' })
  @IsString()
  buyerFaxNumber: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  buyerEmail: string;

  @ApiProperty({ example: 'Ngân hàng Quân đội MB' })
  @IsString()
  buyerBankName: string;

  @ApiProperty({ example: '*****************' })
  @IsString()
  buyerBankAccount: string;

  @ApiProperty({ example: '3' })
  @IsString()
  buyerIdType: string;

  @ApiProperty({ example: '**********' })
  @IsString()
  buyerIdNo: string;

  @ApiProperty({ example: '832472343b_b' })
  @IsString()
  buyerCode: string;

  @ApiProperty({ example: '' })
  @IsString()
  buyerBirthDay: string;
}

export class PaymentDto {
  @ApiProperty({ example: 'Truyền trực tiếp giá trị mong muốn vào đây' })
  @IsString()
  paymentMethodName: string;
}

export class TaxBreakdownDto {
  @ApiProperty({ example: 10 })
  @IsNumber()
  taxPercentage: number;

  @ApiProperty({ example: 3952730 })
  @IsNumber()
  taxableAmount: number;

  @ApiProperty({ example: 395273 })
  @IsNumber()
  taxAmount: number;
}

export class ItemInfoDto {
  @ApiProperty({ example: 1 })
  @IsNumber()
  lineNumber: number;

  @ApiProperty({ example: 'HH0001' })
  @IsString()
  itemCode: string;

  @ApiProperty({ example: 'Hàng hóa 01' })
  @IsString()
  itemName: string;

  @ApiProperty({ example: 'Chiếc' })
  @IsString()
  unitName: string;

  @ApiProperty({ example: 150450 })
  @IsNumber()
  unitPrice: number;

  @ApiProperty({ example: 10 })
  @IsNumber()
  quantity: number;

  @ApiProperty({ example: 1 })
  @IsNumber()
  selection: number;

  @ApiProperty({ example: 1504500 })
  @IsNumber()
  itemTotalAmountWithoutTax: number;

  @ApiProperty({ example: 10 })
  @IsNumber()
  taxPercentage: number;

  @ApiProperty({ example: 150450 })
  @IsNumber()
  taxAmount: number;

  @ApiProperty({ example: null })
  @IsOptional()
  discount: number | null;

  @ApiProperty({ example: null })
  @IsOptional()
  discount2: number | null;

  @ApiProperty({ example: 0 })
  @IsNumber()
  itemDiscount: number;

  @ApiProperty({ example: null })
  @IsOptional()
  itemNote: string | null;

  @ApiProperty({ example: null })
  @IsOptional()
  batchNo: string | null;

  @ApiProperty({ example: null })
  @IsOptional()
  expDate: string | null;
}

export class SummarizeInfoDto {
  @ApiProperty({ example: '{ Tiền phí đặc biệt, Tiền phí, }' })
  @IsString()
  extraName: string;

  @ApiProperty({ example: '{ 00 ,00,}' })
  @IsString()
  extraValue: string;
}

export class CreateInvoiceDto {
  @ApiProperty({ type: GeneralInvoiceInfoDto })
  @ValidateNested()
  @Type(() => GeneralInvoiceInfoDto)
  generalInvoiceInfo: GeneralInvoiceInfoDto;

  @ApiProperty({ type: SellerInfoDto })
  @ValidateNested()
  @Type(() => SellerInfoDto)
  sellerInfo: SellerInfoDto;

  @ApiProperty({ type: BuyerInfoDto })
  @ValidateNested()
  @Type(() => BuyerInfoDto)
  buyerInfo: BuyerInfoDto;

  @ApiProperty({ type: [PaymentDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PaymentDto)
  payments: PaymentDto[];

  @ApiProperty({ type: [TaxBreakdownDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaxBreakdownDto)
  taxBreakdowns: TaxBreakdownDto[];

  @ApiProperty({ type: [ItemInfoDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ItemInfoDto)
  itemInfo: ItemInfoDto[];

  @ApiProperty({ type: SummarizeInfoDto })
  @ValidateNested()
  @Type(() => SummarizeInfoDto)
  summarizeInfo: SummarizeInfoDto;
}
