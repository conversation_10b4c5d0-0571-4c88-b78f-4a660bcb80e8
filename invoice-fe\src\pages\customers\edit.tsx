import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCustomerStore } from "../../stores/customer.store";
import { EditCustomerForm } from "../../components/customers/edit-customer-form";
import { UpdateCustomerRequest } from "../../types";

export default function EditCustomerPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { selectedCustomer, fetchCustomerById, updateCustomer, isLoading } =
    useCustomerStore();

  useEffect(() => {
    if (id) {
      fetchCustomerById(id);
    }
  }, [id, fetchCustomerById]);

  const handleSubmit = async (data: UpdateCustomerRequest) => {
    if (id) {
      await updateCustomer(id, data);
      navigate("/customers");
    }
  };

  const handleCancel = () => {
    navigate("/customers");
  };

  if (!selectedCustomer) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-6">
      <EditCustomerForm
        customer={selectedCustomer}
        isLoading={isLoading}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
}
