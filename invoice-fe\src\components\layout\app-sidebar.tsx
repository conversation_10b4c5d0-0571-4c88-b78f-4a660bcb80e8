import {
  Home,
  FileText,
  Users,
  Settings,
  BarChart3,
  CreditCard,
  User,
  HelpCircle,
} from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../ui/sidebar";
import { useAuthStore } from "../../stores/auth.store";
import { UserRole } from "../../types";

// Navigation items
const navigationItems = [
  {
    title: "Dashboard",
    url: "/",
    icon: Home,
    roles: [UserRole.ADMIN, UserRole.CUSTOMER],
  },
  {
    title: "Invoices",
    url: "/invoices",
    icon: FileText,
    roles: [UserRole.ADMIN, UserRole.CUSTOMER],
  },
  {
    title: "Customers",
    url: "/customers",
    icon: Users,
    roles: [UserRole.ADMIN],
  },
  {
    title: "Reports",
    url: "/reports",
    icon: BarChart3,
    roles: [UserRole.ADMIN],
  },
  {
    title: "Payments",
    url: "/payments",
    icon: CreditCard,
    roles: [UserRole.ADMIN, UserRole.CUSTOMER],
  },
];

const accountItems = [
  {
    title: "Profile",
    url: "/profile",
    icon: User,
    roles: [UserRole.ADMIN, UserRole.CUSTOMER],
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings,
    roles: [UserRole.ADMIN, UserRole.CUSTOMER],
  },
  {
    title: "Help",
    url: "/help",
    icon: HelpCircle,
    roles: [UserRole.ADMIN, UserRole.CUSTOMER],
  },
];

export function AppSidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuthStore();

  const filterItemsByRole = (items: typeof navigationItems) => {
    return items.filter((item) => user?.role && item.roles.includes(user.role));
  };

  const isActive = (url: string) => {
    if (url === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(url);
  };

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center gap-2 px-2 py-2">
          <div className="h-8 w-8 bg-primary rounded flex items-center justify-center">
            <FileText className="h-4 w-4 text-primary-foreground" />
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-semibold">Invoice Service</span>
            <span className="text-xs text-muted-foreground">
              {user?.role === UserRole.ADMIN
                ? "Admin Panel"
                : "Customer Portal"}
            </span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filterItemsByRole(navigationItems).map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive(item.url)}
                    onClick={() => navigate(item.url)}
                  >
                    <button className="flex items-center gap-2 w-full">
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </button>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Account</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filterItemsByRole(accountItems).map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive(item.url)}
                    onClick={() => navigate(item.url)}
                  >
                    <button className="flex items-center gap-2 w-full">
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </button>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border">
        <div className="px-2 py-2">
          <div className="text-xs text-muted-foreground">
            Logged in as <span className="font-medium">{user?.username}</span>
          </div>
          <div className="text-xs text-muted-foreground">{user?.email}</div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
